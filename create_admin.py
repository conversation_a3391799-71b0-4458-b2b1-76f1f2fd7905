#!/usr/bin/env python3
"""
سكريبت لإنشاء حساب أدمن جديد أو تحديث الحساب الموجود
"""

import sys
import os
from werkzeug.security import generate_password_hash

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import db, User, Role

def create_or_update_admin():
    """إنشاء أو تحديث حساب الأدمن"""
    
    with app.app_context():
        print("بحث عن حساب الأدمن الحالي...")
        
        # البحث عن حساب أدمن موجود (بأي اسم مستخدم)
        admin = User.query.filter_by(role=Role.ADMIN).first()
        
        if admin:
            print(f"تم العثور على حساب أدمن موجود: {admin.username}")
            print(f"البريد الإلكتروني: {admin.email}")
            print(f"الدور: {admin.role}")
            print(f"نشط: {admin.is_active}")
            
            # تحديث كلمة المرور
            new_password = 'Admin123!'
            admin.password = generate_password_hash(new_password)
            admin._is_active = True  # التأكد من أن الحساب مفعل
            
            try:
                db.session.commit()
                print(f"✓ تم تحديث كلمة المرور بنجاح!")
                print(f"كلمة المرور الجديدة: {new_password}")
                return True
            except Exception as e:
                db.session.rollback()
                print(f"✗ خطأ في تحديث كلمة المرور: {str(e)}")
                return False
        else:
            print("لم يتم العثور على حساب أدمن. إنشاء حساب جديد...")
            
            # إنشاء حساب أدمن جديد
            new_password = 'Admin123!'
            admin = User(
                username='admin',
                email='<EMAIL>',
                password=generate_password_hash(new_password),
                role=Role.ADMIN,
                phone_number='0555123456',
                wilaya_code='16',  # الجزائر العاصمة
                _is_active=True
            )
            
            try:
                db.session.add(admin)
                db.session.commit()
                print(f"✓ تم إنشاء حساب أدمن جديد بنجاح!")
                print(f"اسم المستخدم: admin")
                print(f"البريد الإلكتروني: <EMAIL>")
                print(f"كلمة المرور: {new_password}")
                return True
            except Exception as e:
                db.session.rollback()
                print(f"✗ خطأ في إنشاء حساب الأدمن: {str(e)}")
                return False

def list_all_users():
    """عرض جميع المستخدمين في النظام"""
    
    with app.app_context():
        print("\nجميع المستخدمين في النظام:")
        print("=" * 50)
        
        users = User.query.all()
        
        if not users:
            print("لا توجد مستخدمين في النظام")
            return
        
        for user in users:
            print(f"المستخدم: {user.username}")
            print(f"البريد: {user.email}")
            print(f"الدور: {user.role}")
            print(f"نشط: {user.is_active}")
            print("-" * 30)

if __name__ == "__main__":
    print("سكريبت إنشاء/تحديث حساب الأدمن")
    print("=" * 50)
    
    # عرض المستخدمين الحاليين
    list_all_users()
    
    # تأكيد من المستخدم
    print("\nهل تريد إنشاء أو تحديث حساب الأدمن؟")
    confirm = input("اكتب 'نعم' أو 'y' للمتابعة: ")
    
    if confirm.lower() in ['y', 'yes', 'نعم']:
        success = create_or_update_admin()
        if success:
            print("\n" + "=" * 50)
            print("✓ تم إنشاء/تحديث حساب الأدمن بنجاح!")
            print("يمكنك الآن تسجيل الدخول باستخدام:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: Admin123!")
            print("=" * 50)
        else:
            print("\n✗ فشل في إنشاء/تحديث حساب الأدمن!")
    else:
        print("تم إلغاء العملية.")
