#!/usr/bin/env python3
"""
سكريبت لتحديث قاعدة البيانات لإضافة ميزة تتبع الحسابات المهجورة
"""

import sys
import os
from datetime import datetime, timezone

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import db, User

def update_database():
    """تحديث قاعدة البيانات لإضافة حقل last_login"""
    
    with app.app_context():
        print("بدء تحديث قاعدة البيانات...")
        print("=" * 50)
        
        try:
            # التحقق من وجود حقل last_login
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('user')]
            
            if 'last_login' not in columns:
                print("إضافة حقل last_login إلى جدول المستخدمين...")
                
                # إضافة العمود الجديد
                with db.engine.connect() as conn:
                    conn.execute(db.text('ALTER TABLE user ADD COLUMN last_login DATETIME'))
                    conn.commit()
                
                print("✓ تم إضافة حقل last_login بنجاح")
            else:
                print("✓ حقل last_login موجود بالفعل")
            
            # تحديث last_login للمستخدمين الحاليين
            print("\nتحديث تواريخ آخر دخول للمستخدمين الحاليين...")
            
            users = User.query.all()
            updated_count = 0
            
            for user in users:
                if user.last_login is None:
                    # تعيين تاريخ الإنشاء كآخر دخول للمستخدمين الذين لم يسجلوا دخول
                    user.last_login = user.created_at
                    updated_count += 1
                    print(f"  - تحديث {user.username}: {user.created_at}")
            
            if updated_count > 0:
                db.session.commit()
                print(f"\n✓ تم تحديث {updated_count} مستخدم")
            else:
                print("\n✓ جميع المستخدمين لديهم تواريخ آخر دخول")
            
            print("\n" + "=" * 50)
            print("✓ تم تحديث قاعدة البيانات بنجاح!")
            
            # عرض إحصائيات الحسابات المهجورة
            print("\nإحصائيات الحسابات المهجورة:")
            print("=" * 50)
            
            abandoned_accounts = User.get_abandoned_accounts(days=180)
            print(f"إجمالي الحسابات المهجورة (أكثر من 6 أشهر): {len(abandoned_accounts)}")
            
            never_logged_in = [u for u in abandoned_accounts if not u.last_login or u.last_login == u.created_at]
            print(f"الحسابات التي لم تسجل دخول أبداً: {len(never_logged_in)}")
            
            inactive_long_time = [u for u in abandoned_accounts if u.last_login and u.last_login != u.created_at]
            print(f"الحسابات غير النشطة لفترة طويلة: {len(inactive_long_time)}")
            
            if abandoned_accounts:
                print("\nتفاصيل الحسابات المهجورة:")
                print("-" * 30)
                for user in abandoned_accounts:
                    days_inactive = user.days_since_last_login
                    status = "لم يسجل دخول أبداً" if not user.last_login or user.last_login == user.created_at else f"آخر دخول: {user.last_login.strftime('%Y-%m-%d')}"
                    print(f"{user.username} ({user.role}) - {days_inactive} يوم - {status}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"✗ حدث خطأ أثناء تحديث قاعدة البيانات: {str(e)}")
            return False

if __name__ == '__main__':
    success = update_database()
    if success:
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("يمكنك الآن استخدام ميزة إدارة الحسابات المهجورة من لوحة الإدارة المتقدمة.")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات!")
        sys.exit(1)
