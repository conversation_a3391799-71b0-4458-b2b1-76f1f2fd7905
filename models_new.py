"""
نماذج قاعدة البيانات الجديدة
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime

# تهيئة قاعدة البيانات
db = SQLAlchemy()

# أدوار المستخدمين
class Role:
    ADMIN = 'admin'
    INSPECTOR = 'inspector'
    TEACHER = 'teacher'
    USER_MANAGER = 'user_manager'  # دور جديد لإدارة المستخدمين بصلاحيات محدودة

# نموذج إعدادات الأدوار المتاحة للتسجيل
class RoleSettings(db.Model):
    """نموذج لإدارة الأدوار المتاحة للتسجيل"""
    id = db.Column(db.Integer, primary_key=True)
    role_name = db.Column(db.String(20), unique=True, nullable=False)  # اسم الدور
    is_enabled = db.Column(db.<PERSON><PERSON>, default=True)  # هل الدور متاح للتسجيل
    display_name = db.Column(db.String(50), nullable=False)  # الاسم المعروض
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# قائمة الولايات الجزائرية (التقسيم الإداري الجديد - 58 ولاية)
ALGERIAN_WILAYAS = [
    ('01', 'أدرار'),
    ('02', 'الشلف'),
    ('03', 'الأغواط'),
    ('04', 'أم البواقي'),
    ('05', 'باتنة'),
    ('06', 'بجاية'),
    ('07', 'بسكرة'),
    ('08', 'بشار'),
    ('09', 'البليدة'),
    ('10', 'البويرة'),
    ('11', 'تمنراست'),
    ('12', 'تبسة'),
    ('13', 'تلمسان'),
    ('14', 'تيارت'),
    ('15', 'تيزي وزو'),
    ('16', 'الجزائر'),
    ('17', 'الجلفة'),
    ('18', 'جيجل'),
    ('19', 'سطيف'),
    ('20', 'سعيدة'),
    ('21', 'سكيكدة'),
    ('22', 'سيدي بلعباس'),
    ('23', 'عنابة'),
    ('24', 'قالمة'),
    ('25', 'قسنطينة'),
    ('26', 'المدية'),
    ('27', 'مستغانم'),
    ('28', 'المسيلة'),
    ('29', 'معسكر'),
    ('30', 'ورقلة'),
    ('31', 'وهران'),
    ('32', 'البيض'),
    ('33', 'إليزي'),
    ('34', 'برج بوعريريج'),
    ('35', 'بومرداس'),
    ('36', 'الطارف'),
    ('37', 'تندوف'),
    ('38', 'تيسمسيلت'),
    ('39', 'الوادي'),
    ('40', 'خنشلة'),
    ('41', 'سوق أهراس'),
    ('42', 'تيبازة'),
    ('43', 'ميلة'),
    ('44', 'عين الدفلى'),
    ('45', 'النعامة'),
    ('46', 'عين تموشنت'),
    ('47', 'غرداية'),
    ('48', 'غليزان'),
    ('49', 'تيميمون'),
    ('50', 'برج باجي مختار'),
    ('51', 'أولاد جلال'),
    ('52', 'بني عباس'),
    ('53', 'عين صالح'),
    ('54', 'عين قزام'),
    ('55', 'تقرت'),
    ('56', 'جانت'),
    ('57', 'المغير'),
    ('58', 'المنيعة')
]

# نموذج المستخدم
class User(db.Model, UserMixin):
    """نموذج المستخدم مع دعم تسجيل الدخول"""

    @property
    def is_authenticated(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False)
    _is_active = db.Column('is_active', db.Boolean, default=True)  # لتفعيل أو تعطيل الحساب

    # الحقول الجديدة
    phone_number = db.Column(db.String(15), nullable=False)  # رقم الهاتف (إجباري)
    wilaya_code = db.Column(db.String(2), nullable=True)  # رمز الولاية (اختياري)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)  # آخر تسجيل دخول

    # العلاقات
    schedules = db.relationship('Schedule', backref='user', lazy=True)
    progress_entries = db.relationship('ProgressEntry', backref='user', lazy=True)

    @property
    def wilaya_name(self):
        """الحصول على اسم الولاية من الرمز"""
        if not self.wilaya_code:
            return None
        wilaya_dict = dict(ALGERIAN_WILAYAS)
        return wilaya_dict.get(self.wilaya_code, 'غير محدد')

    @property
    def formatted_phone(self):
        """تنسيق رقم الهاتف للعرض"""
        if not self.phone_number:
            return None
        # تنسيق الرقم الجزائري (مثال: 0555123456 -> 0555 12 34 56)
        phone = self.phone_number.replace(' ', '').replace('-', '')
        if len(phone) == 10 and phone.startswith('0'):
            return f"{phone[:4]} {phone[4:6]} {phone[6:8]} {phone[8:]}"
        return phone

    @property
    def masked_phone(self):
        """إخفاء جزئي لرقم الهاتف للحماية"""
        if not self.phone_number:
            return None
        phone = self.phone_number.replace(' ', '').replace('-', '')
        if len(phone) == 10 and phone.startswith('0'):
            # إظهار أول 4 أرقام وآخر رقمين فقط (مثال: 0555****56)
            return f"{phone[:4]}****{phone[-2:]}"
        return phone[:3] + '*' * (len(phone) - 5) + phone[-2:] if len(phone) > 5 else phone

    @property
    def masked_email(self):
        """إخفاء جزئي للبريد الإلكتروني للحماية"""
        if not self.email:
            return None
        if '@' in self.email:
            local, domain = self.email.split('@', 1)
            if len(local) <= 2:
                masked_local = local
            elif len(local) <= 4:
                masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
            else:
                masked_local = local[:2] + '*' * (len(local) - 4) + local[-2:]
            return f"{masked_local}@{domain}"
        return self.email

    @property
    def masked_username(self):
        """إخفاء جزئي لاسم المستخدم للحماية"""
        if not self.username:
            return None
        if len(self.username) <= 3:
            return self.username
        elif len(self.username) <= 6:
            return self.username[:2] + '*' * (len(self.username) - 3) + self.username[-1:]
        else:
            return self.username[:3] + '*' * (len(self.username) - 6) + self.username[-3:]

    @property
    def is_abandoned(self):
        """التحقق من كون الحساب مهجور (لم يسجل دخول لأكثر من 6 أشهر)"""
        if not self.last_login:
            # إذا لم يسجل دخول أبداً، نعتبر تاريخ الإنشاء
            return (datetime.utcnow() - self.created_at).days > 180
        return (datetime.utcnow() - self.last_login).days > 180

    @property
    def days_since_last_login(self):
        """عدد الأيام منذ آخر تسجيل دخول"""
        if not self.last_login:
            return (datetime.utcnow() - self.created_at).days
        return (datetime.utcnow() - self.last_login).days

    @classmethod
    def get_abandoned_accounts(cls, days=180):
        """الحصول على الحسابات المهجورة"""
        from datetime import timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        # الحسابات التي لم تسجل دخول أبداً ومضى على إنشائها أكثر من المدة المحددة
        never_logged_in = cls.query.filter(
            cls.last_login.is_(None),
            cls.created_at < cutoff_date,
            cls.role != Role.ADMIN  # استثناء حسابات الأدمن
        )

        # الحسابات التي سجلت دخول لكن آخر دخول كان قبل المدة المحددة
        inactive_accounts = cls.query.filter(
            cls.last_login < cutoff_date,
            cls.role != Role.ADMIN  # استثناء حسابات الأدمن
        )

        # دمج النتائج
        return never_logged_in.union(inactive_accounts).all()

    # للمفتشين: الأساتذة الذين يشرفون عليهم
    supervised_teachers = db.relationship('User',
                                         secondary='inspector_teacher',
                                         primaryjoin="and_(User.id==inspector_teacher.c.inspector_id, User.role=='inspector')",
                                         secondaryjoin="and_(User.id==inspector_teacher.c.teacher_id, User.role=='teacher')",
                                         backref=db.backref('inspectors', lazy='dynamic'),
                                         lazy='dynamic')

# علاقة المفتش-الأستاذ
inspector_teacher = db.Table('inspector_teacher',
    db.Column('inspector_id', db.Integer, db.ForeignKey('user.id'), primary_key=True),
    db.Column('teacher_id', db.Integer, db.ForeignKey('user.id'), primary_key=True)
)

# نموذج المستوى التعليمي
class EducationalLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)  # لتفعيل أو تعطيل المستوى
    database_prefix = db.Column(db.String(20), nullable=True)  # بادئة لقاعدة البيانات الخاصة بالمستوى

    # العلاقات
    subjects = db.relationship('Subject', backref='educational_level', lazy=True)
    databases = db.relationship('LevelDatabase', backref='level', lazy=True, cascade='all, delete-orphan')

# نموذج المادة الدراسية
class Subject(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    domains = db.relationship('Domain', backref='subject', lazy=True)

# نموذج الميدان
class Domain(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)

    # العلاقات
    knowledge_materials = db.relationship('KnowledgeMaterial', backref='domain', lazy=True)

# نموذج المادة المعرفية
class KnowledgeMaterial(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    domain_id = db.Column(db.Integer, db.ForeignKey('domain.id'), nullable=False)

    # العلاقات
    competencies = db.relationship('Competency', backref='knowledge_material', lazy=True)

# نموذج الكفاءة المستهدفة
class Competency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.Text, nullable=False)
    knowledge_material_id = db.Column(db.Integer, db.ForeignKey('knowledge_material.id'), nullable=False)

    # العلاقات
    progress_entries = db.relationship('ProgressEntry', backref='competency', lazy=True)

# نموذج جدول الأستاذ
class Schedule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0=الاثنين، 6=الأحد
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    subject = db.relationship('Subject')
    level = db.relationship('EducationalLevel')

# نموذج تسجيل التقدم
class ProgressEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    competency_id = db.Column(db.Integer, db.ForeignKey('competency.id'), nullable=True)

    # إضافة العلاقات مع المستوى والمادة والميدان والمادة المعرفية
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    domain_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    material_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))

    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)  # 'completed', 'in_progress', 'planned'
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    level = db.relationship('EducationalLevel', foreign_keys=[level_id], backref='progress_entries')
    subject = db.relationship('LevelDataEntry', foreign_keys=[subject_id], backref='subject_progress_entries')
    domain = db.relationship('LevelDataEntry', foreign_keys=[domain_id], backref='domain_progress_entries')
    material = db.relationship('LevelDataEntry', foreign_keys=[material_id], backref='material_progress_entries')

# نموذج قاعدة البيانات الخاصة بكل مستوى
class LevelDatabase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)  # اسم قاعدة البيانات
    file_path = db.Column(db.String(255), nullable=False)  # مسار ملف قاعدة البيانات
    is_active = db.Column(db.Boolean, default=True)  # حالة قاعدة البيانات
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    data_entries = db.relationship('LevelDataEntry', backref='database', lazy=True, cascade='all, delete-orphan')

# نموذج بيانات كل مستوى
class LevelDataEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    database_id = db.Column(db.Integer, db.ForeignKey('level_database.id'), nullable=False)
    entry_type = db.Column(db.String(50), nullable=False)  # نوع البيانات (مادة، ميدان، مادة معرفية، كفاءة)
    parent_id = db.Column(db.Integer, nullable=True)  # معرف العنصر الأب (للتسلسل الهرمي)
    name = db.Column(db.String(255), nullable=False)  # اسم العنصر
    description = db.Column(db.Text, nullable=True)  # وصف العنصر
    order_num = db.Column(db.Integer, default=0)  # ترتيب العنصر
    is_active = db.Column(db.Boolean, default=True)  # حالة العنصر
    extra_data = db.Column(db.Text, nullable=True)  # بيانات إضافية (JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# نموذج الإشعارات بين الإدارة والمفتشين
class AdminInspectorNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_admin_inspector_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_admin_inspector_notifications')


# نموذج الإشعارات بين المفتشين والأساتذة
class InspectorTeacherNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_inspector_teacher_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_inspector_teacher_notifications')

# نموذج الإشعارات العامة (للجميع أو لمجموعة محددة)
class GeneralNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    target_type = db.Column(db.String(20), nullable=False)  # 'all', 'role', 'specific'
    target_role = db.Column(db.String(20), nullable=True)  # للإرسال لدور معين
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_general_notifications')

# نموذج قراءة الإشعارات العامة
class GeneralNotificationRead(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    notification_id = db.Column(db.Integer, db.ForeignKey('general_notification.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    read_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    notification = db.relationship('GeneralNotification', backref='reads')
    user = db.relationship('User', backref='general_notification_reads')
