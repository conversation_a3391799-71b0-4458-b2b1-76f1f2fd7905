{% extends "base.html" %}

{% block title %}إحصائيات متقدمة - Ta9affi{% endblock %}

{% block styles %}
<style>
    .stats-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .progress-animated {
        transition: width 1s ease-in-out;
    }

    .counter-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }

    .animated-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    .level-tab {
        border-radius: 10px 10px 0 0;
        margin-left: 5px;
    }

    .level-tab.active {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-color: #007bff;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
        transform: scale(1.02);
        transition: all 0.3s ease;
    }

    .badge-animated {
        animation: bounce 2s infinite;
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-10px);
        }

        60% {
            transform: translateY(-5px);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="text-primary">
                    <i class="fas fa-chart-line animated-icon me-2"></i>
                    إحصائيات متقدمة
                </h2>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{{ url_for('recalculate_stats') }}" class="btn btn-outline-success">
                        <i class="fas fa-sync-alt me-1"></i>
                        إعادة حساب الإحصائيات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- ملخص التقدم الإجمالي -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card stats-card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-chart-pie me-1"></i>
                    ملخص التقدم الإجمالي
                    <br>
                    <small class="text-light">
                        <i class="fas fa-info-circle me-1"></i>
                        يعرض هذا القسم إجمالي الموارد المعرفية في جميع المستويات التعليمية
                    </small>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6>
                                <i class="fas fa-chart-line animated-icon me-2"></i>
                                نسبة الإنجاز الإجمالية (بناءً على الموارد المعرفية)
                            </h6>
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-success progress-animated" role="progressbar"
                                    data-completion-rate="{{ (completion_rate|default(0))|round|int }}"
                                    aria-valuemin="0" aria-valuemax="100">
                                    {{ (completion_rate|default(0))|round|int }}%
                                </div>
                            </div>
                            <div class="row text-center mt-3">
                                <div class="col-md-3">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <div class="counter-number text-success"
                                            data-target="{{ progress_stats.completed|default(0) }}">0</div>
                                        <small class="text-muted">مكتملة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-warning bg-opacity-10 rounded">
                                        <div class="counter-number text-warning"
                                            data-target="{{ progress_stats.in_progress|default(0) }}">0</div>
                                        <small class="text-muted">قيد التنفيذ</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-danger bg-opacity-10 rounded">
                                        <div class="counter-number text-danger"
                                            data-target="{{ progress_stats.planned|default(0) }}">0</div>
                                        <small class="text-muted">مخططة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 bg-primary bg-opacity-10 rounded">
                                        <div class="counter-number text-primary"
                                            data-target="{{ progress_stats.total|default(0) }}">0</div>
                                        <small class="text-muted">الإجمالي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم التبويبات للمستويات التعليمية -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card stats-card">
                <div class="card-header bg-gradient"
                    style="background: linear-gradient(135deg, #007bff, #0056b3); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        التقدم التفصيلي حسب المستوى التعليمي
                    </h5>
                </div>
                <div class="card-body">
                    <!-- تبويبات المستويات -->
                    <ul class="nav nav-tabs" id="levelTabs" role="tablist">
                        {% for level_id, stats in level_stats.items() %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link level-tab {% if loop.first %}active{% endif %}"
                                id="level-{{ level_id }}-tab" data-bs-toggle="tab"
                                data-bs-target="#level-{{ level_id }}" type="button" role="tab"
                                aria-controls="level-{{ level_id }}"
                                aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                {{ stats.name }}
                                <span class="badge bg-light text-dark ms-2 badge-animated">{{
                                    (stats.completion_rate|default(0))|round|int }}%</span>
                            </button>
                        </li>
                        {% endfor %}
                    </ul>

                    <!-- محتوى التبويبات -->
                    <div class="tab-content mt-3" id="levelTabsContent">
                        {% for level_id, stats in level_stats.items() %}
                        <div class="tab-pane fade {% if loop.first %}show active{% endif %}" id="level-{{ level_id }}"
                            role="tabpanel" aria-labelledby="level-{{ level_id }}-tab">

                            <!-- إحصائيات المستوى -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                        <div class="h3 text-primary">{{
                                            (stats.completion_rate|default(0))|round|int }}%
                                        </div>
                                        <small class="text-muted">نسبة الإنجاز</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                        <div class="h3 text-success">{{
                                            stats.completed_materials|default(0) }}</div>
                                        <small class="text-muted">موارد مكتملة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                        <div class="h3 text-info">{{
                                            stats.total_materials|default(0) }}</div>
                                        <small class="text-muted">إجمالي الموارد</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <div class="h3 text-warning">{{
                                            (stats.total_materials|default(0)) -
                                            (stats.completed_materials|default(0)) }}</div>
                                        <small class="text-muted">موارد متبقية</small>
                                    </div>
                                </div>
                            </div>

                            <!-- شريط التقدم للمستوى -->
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-success progress-animated" role="progressbar"
                                    data-completion-rate="{{ (stats.completion_rate|default(0))|round|int }}"
                                    aria-valuemin="0" aria-valuemax="100">
                                    {{ (stats.completion_rate|default(0))|round|int }}%
                                </div>
                            </div>

                            <!-- التقدم حسب المادة في هذا المستوى -->
                            {% if stats.completed_by_subject %}
                            <div class="table-responsive">
                                <table class="table table-sm table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><i class="fas fa-book me-1"></i> المادة الدراسية</th>
                                            <th><i class="fas fa-check-circle me-1"></i> موارد مكتملة</th>
                                            <th><i class="fas fa-list me-1"></i> إجمالي الموارد</th>
                                            <th><i class="fas fa-percentage me-1"></i> النسبة المئوية</th>
                                            <th><i class="fas fa-chart-bar me-1"></i> التقدم</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for subject_name, completed in stats.completed_by_subject.items() %}
                                        {% set total = stats.total_by_subject.get(subject_name, 0) %}
                                        {% set percentage = ((completed / total * 100) if total > 0 else 0)|round|int %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-book me-1 text-primary"></i>
                                                {{ subject_name }}
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ completed }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ total }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-primary">{{ percentage }}%</strong>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px; width: 100px;">
                                                    <div class="progress-bar progress-animated
                                                    {% if percentage >= 80 %}bg-success
                                                    {% elif percentage >= 60 %}bg-warning
                                                    {% elif percentage >= 40 %}bg-info
                                                    {% else %}bg-danger
                                                    {% endif %}" role="progressbar" data-percentage="{{ percentage }}"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد بيانات تقدم متاحة لهذا المستوى حالياً
                                <br>
                                <small class="text-muted">
                                    يمكنك البدء بتسجيل التقدم من صفحة
                                    <a href="{{ url_for('teaching_program') }}" class="alert-link">البرنامج التعليمي</a>
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول آخر تحديثات التقدم -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card stats-card">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-tasks me-1"></i>
                            آخر تحديثات التقدم
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('teaching_program') }}" class="btn btn-sm btn-light">
                                <i class="fas fa-plus me-1"></i> إضافة تقدم جديد
                            </a>
                            <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse"
                                data-bs-target="#filterOptions" aria-expanded="false" aria-controls="filterOptions">
                                <i class="fas fa-filter me-1"></i> خيارات التصفية
                            </button>
                        </div>
                    </div>
                </div>

                <!-- خيارات التصفية -->
                <div class="collapse" id="filterOptions">
                    <div class="card-body border-bottom">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="levelFilter" class="form-label">تصفية حسب المستوى:</label>
                                <select class="form-select" id="levelFilter" onchange="filterTable()">
                                    <option value="">جميع المستويات</option>
                                    {% for level_id, stats in level_stats.items() %}
                                    <option value="{{ level_id }}">{{ stats.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="subjectFilter" class="form-label">تصفية حسب المادة:</label>
                                <select class="form-select" id="subjectFilter" onchange="filterTable()">
                                    <option value="">جميع المواد</option>
                                    {% for subject_id, stats in subject_stats.items() %}
                                    <option value="{{ subject_id }}">{{ stats.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="statusFilter" class="form-label">تصفية حسب الحالة:</label>
                                <select class="form-select" id="statusFilter" onchange="filterTable()">
                                    <option value="">جميع الحالات</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="in_progress">قيد التنفيذ</option>
                                    <option value="planned">مخططة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchInput" class="form-label">البحث:</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="ابحث في التقدم..."
                                    onkeyup="filterTable()">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="progressTable">
                            <thead class="table-dark">
                                <tr>
                                    <th class="text-center">
                                        <i class="fas fa-calendar me-1"></i> التاريخ
                                    </th>
                                    <th>
                                        <i class="fas fa-layer-group me-1"></i> المستوى
                                    </th>
                                    <th>
                                        <i class="fas fa-book me-1"></i> المادة
                                    </th>
                                    <th>
                                        <i class="fas fa-sitemap me-1"></i> الميدان
                                    </th>
                                    <th>
                                        <i class="fas fa-lightbulb me-1"></i> الكفاءة
                                    </th>
                                    <th>
                                        <i class="fas fa-puzzle-piece me-1"></i> المورد المعرفي
                                    </th>
                                    <th class="text-center">
                                        <i class="fas fa-info-circle me-1"></i> الحالة
                                    </th>
                                    <th class="text-center">
                                        <i class="fas fa-chalkboard me-1"></i> الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if progress_entries %}
                                {% for entry in progress_entries %}
                                <tr data-level="{{ entry.level.id if entry.level else '' }}"
                                    data-subject="{{ entry.subject.id if entry.subject else '' }}"
                                    data-status="{{ entry.status }}"
                                    class="{% if entry.status == 'completed' %}table-success{% elif entry.status == 'in_progress' %}table-warning{% elif entry.status == 'planned' %}table-danger{% endif %}">
                                    <td class="text-center">{{ entry.date.strftime('%Y-%m-%d') if entry.date else '' }}
                                    </td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.name if entry.competency else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td class="text-center">
                                        {% if entry.status == 'completed' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>مكتملة
                                        </span>
                                        {% elif entry.status == 'in_progress' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>قيد التنفيذ
                                        </span>
                                        {% elif entry.status == 'planned' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-calendar-plus me-1"></i>مخططة
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle fa-2x mb-2"></i>
                                            <p>لا توجد تحديثات تقدم مسجلة بعد</p>
                                            <a href="{{ url_for('teaching_program') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>
                                                إضافة أول تقدم
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // تحريك العدادات
    document.addEventListener('DOMContentLoaded', function () {
        // تحريك العدادات
        const counters = document.querySelectorAll('.counter-number');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const increment = target / 50;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current);
            }, 30);
        });

        // تحريك أشرطة التقدم
        setTimeout(() => {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const rate = bar.getAttribute('data-completion-rate') || bar.getAttribute('data-percentage');
                if (rate) {
                    bar.style.width = rate + '%';
                }
            });
        }, 500);
    });

    // تصفية الجدول
    function filterTable() {
        const levelFilter = document.getElementById('levelFilter').value;
        const subjectFilter = document.getElementById('subjectFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const searchInput = document.getElementById('searchInput').value.toLowerCase();

        const table = document.getElementById('progressTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const level = row.getAttribute('data-level');
            const subject = row.getAttribute('data-subject');
            const status = row.getAttribute('data-status');
            const text = row.textContent.toLowerCase();

            let show = true;

            if (levelFilter && level !== levelFilter) show = false;
            if (subjectFilter && subject !== subjectFilter) show = false;
            if (statusFilter && status !== statusFilter) show = false;
            if (searchInput && !text.includes(searchInput)) show = false;

            row.style.display = show ? '' : 'none';
        }
    }
</script>
{% endblock %}