#!/usr/bin/env python3
"""
سكريبت لتحديث كلمات المرور الحالية لتتوافق مع المتطلبات الجديدة
"""

import sys
import os
from werkzeug.security import generate_password_hash

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import db, User, Role

# كلمات المرور الجديدة القوية للمستخدمين
NEW_PASSWORDS = {
    'admin': 'Admin123!',
    'inspector': 'Inspector123!',
    'teacher': 'Teacher123!',
    'teacher2': 'Teacher123!',
    'teacher3': 'Teacher123!',
    'inspector_test': 'Inspector123!',
    'teacher1_test': 'Teacher123!',
    'teacher2_test': 'Teacher123!',
    'teacher3_test': 'Teacher123!',
    'test_teacher': 'Teacher123!',
    'tahar': 'Tahar123!',
    'saku17': 'Saku123!'
}

def validate_password_strength(password):
    """التحقق من قوة كلمة المرور"""
    import re
    
    if not password:
        return False, "كلمة المرور مطلوبة"
    
    if len(password) <= 6:
        return False, "كلمة المرور يجب أن تكون أكثر من 6 أحرف"
    
    if not re.search(r'[A-Z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"
    
    if not re.search(r'[0-9]', password):
        return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "كلمة المرور يجب أن تحتوي على رمز واحد على الأقل"
    
    return True, "كلمة المرور قوية"

def update_user_passwords():
    """تحديث كلمات المرور لجميع المستخدمين"""
    
    with app.app_context():
        print("بدء تحديث كلمات المرور...")
        print("=" * 50)
        
        # الحصول على جميع المستخدمين
        users = User.query.all()
        updated_count = 0
        
        for user in users:
            # تحديد كلمة المرور الجديدة
            if user.username in NEW_PASSWORDS:
                new_password = NEW_PASSWORDS[user.username]
            else:
                # إنشاء كلمة مرور افتراضية قوية للمستخدمين الآخرين
                role_prefix = {
                    Role.ADMIN: 'Admin',
                    Role.INSPECTOR: 'Inspector',
                    Role.TEACHER: 'Teacher',
                    Role.USER_MANAGER: 'Manager'
                }.get(user.role, 'User')
                new_password = f"{role_prefix}{user.id}23!"
            
            # التحقق من قوة كلمة المرور الجديدة
            is_valid, message = validate_password_strength(new_password)
            
            if is_valid:
                # تحديث كلمة المرور
                user.password = generate_password_hash(new_password)
                updated_count += 1
                
                print(f"✓ تم تحديث كلمة المرور للمستخدم: {user.username}")
                print(f"  الدور: {user.role}")
                print(f"  كلمة المرور الجديدة: {new_password}")
                print(f"  البريد الإلكتروني: {user.email}")
                print("-" * 30)
            else:
                print(f"✗ فشل في تحديث كلمة المرور للمستخدم {user.username}: {message}")
        
        try:
            # حفظ التغييرات
            db.session.commit()
            print("=" * 50)
            print(f"✓ تم تحديث كلمات المرور بنجاح!")
            print(f"عدد المستخدمين المحدثين: {updated_count}")
            print("=" * 50)
            
            # طباعة ملخص كلمات المرور الجديدة
            print("\nملخص كلمات المرور الجديدة:")
            print("=" * 50)
            for user in users:
                if user.username in NEW_PASSWORDS:
                    password = NEW_PASSWORDS[user.username]
                else:
                    role_prefix = {
                        Role.ADMIN: 'Admin',
                        Role.INSPECTOR: 'Inspector',
                        Role.TEACHER: 'Teacher',
                        Role.USER_MANAGER: 'Manager'
                    }.get(user.role, 'User')
                    password = f"{role_prefix}{user.id}23!"
                
                print(f"المستخدم: {user.username}")
                print(f"كلمة المرور: {password}")
                print(f"الدور: {user.role}")
                print("-" * 20)
                
        except Exception as e:
            db.session.rollback()
            print(f"✗ حدث خطأ أثناء حفظ التغييرات: {str(e)}")
            return False
        
        return True

if __name__ == "__main__":
    print("سكريبت تحديث كلمات المرور")
    print("=" * 50)
    
    # تأكيد من المستخدم
    confirm = input("هل تريد المتابعة مع تحديث كلمات المرور؟ (y/N): ")
    
    if confirm.lower() in ['y', 'yes', 'نعم']:
        success = update_user_passwords()
        if success:
            print("\n✓ تم تحديث كلمات المرور بنجاح!")
            print("يمكنك الآن استخدام كلمات المرور الجديدة لتسجيل الدخول.")
        else:
            print("\n✗ فشل في تحديث كلمات المرور!")
    else:
        print("تم إلغاء العملية.")
