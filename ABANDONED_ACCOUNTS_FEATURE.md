# ميزة إدارة الحسابات المهجورة

## الوصف
تم إضافة ميزة جديدة تمكن الأدمن من إدارة الحسابات المهجورة التي لم تسجل دخول لأكثر من 6 أشهر، مع إمكانية تحديدها دفعة واحدة وحذفها نهائياً.

## المميزات الجديدة

### 🔍 **تتبع آخر تسجيل دخول**
- **حقل جديد:** `last_login` في جدول المستخدمين
- **تحديث تلقائي:** يتم تحديث التاريخ عند كل تسجيل دخول
- **حساب أيام الخمول:** عدد الأيام منذ آخر نشاط

### 📊 **تحديد الحسابات المهجورة**
- **المعيار:** أكثر من 6 أشهر (180 يوم) بدون نشاط
- **فئتان:**
  - حسابات لم تسجل دخول أبداً
  - حسابات غير نشطة لفترة طويلة
- **استثناء:** حسابات الأدمن محمية من التصنيف كمهجورة

### 🗑️ **حذف دفعي آمن**
- **تحديد متعدد:** إمكانية تحديد عدة حسابات
- **تأكيد مزدوج:** كلمة مرور الأدمن + تأكيد صريح
- **حذف شامل:** إزالة جميع البيانات المرتبطة

## الصفحات والملفات الجديدة

### 1. **صفحة إدارة الحسابات المهجورة**
- **المسار:** `/admin/abandoned-accounts`
- **الملف:** `templates/admin_abandoned_accounts.html`
- **الوصول:** الأدمن فقط

### 2. **التحديثات على قاعدة البيانات**
- **الحقل الجديد:** `last_login` في نموذج User
- **الدوال الجديدة:**
  - `is_abandoned`: خاصية للتحقق من كون الحساب مهجور
  - `days_since_last_login`: عدد أيام الخمول
  - `get_abandoned_accounts()`: دالة للحصول على الحسابات المهجورة

### 3. **Routes الجديدة**
- **`/admin/abandoned-accounts`:** عرض الحسابات المهجورة
- **`/admin/delete-abandoned-accounts`:** حذف الحسابات المحددة

## كيفية الاستخدام

### 📋 **الوصول للميزة**
1. تسجيل الدخول كأدمن
2. الذهاب إلى "الإدارة المتقدمة"
3. النقر على "عرض الحسابات المهجورة"

### 📊 **عرض الإحصائيات**
- **إجمالي الحسابات المهجورة**
- **الحسابات التي لم تسجل دخول أبداً**
- **الحسابات غير النشطة لفترة طويلة**
- **توزيع حسب الأدوار** (أساتذة، مفتشين، إلخ)

### 🔍 **تفاصيل كل حساب**
- اسم المستخدم والبريد الإلكتروني
- الدور ورقم الهاتف والولاية
- تاريخ الإنشاء وآخر دخول
- عدد أيام الخمول
- حالة الحساب (مفعل/معطل)

### ✅ **تحديد الحسابات للحذف**
- **تحديد فردي:** checkbox لكل حساب
- **تحديد الكل:** زر "تحديد الكل"
- **إلغاء التحديد:** زر "إلغاء التحديد"
- **عداد ديناميكي:** يظهر عدد الحسابات المحددة

### 🗑️ **عملية الحذف الآمنة**
1. تحديد الحسابات المراد حذفها
2. النقر على "حذف المحدد"
3. إدخال كلمة مرور الأدمن
4. تأكيد الموافقة على الحذف النهائي
5. تنفيذ العملية

## الأمان والحماية

### 🔒 **إجراءات الأمان**
- **صلاحيات محدودة:** الأدمن فقط
- **تأكيد مزدوج:** كلمة مرور + checkbox
- **حماية حسابات الأدمن:** لا يمكن حذفها
- **تسجيل العمليات:** في سجل النظام

### 🛡️ **الحذف الشامل**
عند حذف حساب، يتم حذف:
- **سجلات التقدم:** جميع إدخالات ProgressEntry
- **الجداول الزمنية:** جميع Schedule المرتبطة
- **الإشعارات:** AdminInspectorNotification و InspectorTeacherNotification
- **قراءة الإشعارات:** GeneralNotificationRead
- **العلاقات:** علاقات المفتش-الأستاذ

## الاختبار والتطوير

### 🧪 **سكريبت الاختبار**
- **الملف:** `create_test_abandoned_accounts.py`
- **الوظيفة:** إنشاء حسابات اختبار مهجورة
- **البيانات:** 5 حسابات بتواريخ مختلفة

### 🔄 **سكريبت التحديث**
- **الملف:** `update_database_for_abandoned_accounts.py`
- **الوظيفة:** إضافة حقل last_login وتحديث البيانات
- **التشغيل:** مرة واحدة عند التحديث

## الإحصائيات والتقارير

### 📈 **البيانات المعروضة**
- إجمالي الحسابات المهجورة
- تصنيف حسب نوع الخمول
- توزيع حسب الأدوار
- تفاصيل كل حساب مع أيام الخمول

### 📊 **مثال على الإحصائيات**
```
إجمالي الحسابات المهجورة: 4
- لم تسجل دخول أبداً: 2
- غير نشطة لفترة طويلة: 2
- أساتذة مهجورة: 3
- مفتشين مهجورين: 1
```

## التحديثات المطلوبة

### ✅ **تم تنفيذها**
- إضافة حقل `last_login` لنموذج User
- تحديث دالة تسجيل الدخول لتسجيل آخر دخول
- إنشاء صفحة إدارة الحسابات المهجورة
- إضافة routes للعرض والحذف
- تحديث صفحة الإدارة المتقدمة
- إنشاء سكريبتات التحديث والاختبار

### 🔄 **للمستقبل**
- إضافة تنبيهات للحسابات قريبة من أن تصبح مهجورة
- إرسال إشعارات للمستخدمين قبل الحذف
- إضافة خيار أرشفة بدلاً من الحذف
- تقارير دورية عن الحسابات المهجورة

## ملاحظات تقنية

### 🔧 **التحسينات**
- **أداء محسن:** استعلامات قاعدة بيانات محسنة
- **واجهة سهلة:** تصميم بديهي مع Bootstrap
- **JavaScript تفاعلي:** تحديد ديناميكي وتأكيدات
- **رسائل واضحة:** تنبيهات وتأكيدات مفصلة

### 📱 **التوافق**
- **متجاوب:** يعمل على جميع الأجهزة
- **متصفحات حديثة:** متوافق مع جميع المتصفحات
- **إمكانية الوصول:** دعم قارئات الشاشة

---

**تاريخ الإضافة:** 2025-08-02  
**الحالة:** مكتمل ومختبر ✅  
**المطور:** Augment Agent  
**النسخة:** Ta9affi v1.08
