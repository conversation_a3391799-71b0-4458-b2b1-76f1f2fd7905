# نظام حماية البيانات الشخصية للمستخدمين

## الوصف
تم تطوير نظام حماية شامل للبيانات الشخصية للمستخدمين في منصة تقافي، يتضمن تشفير كلمات المرور وإخفاء جزئي للبيانات الحساسة حسب مستوى الصلاحيات.

## 🔒 مستويات الحماية المطبقة

### 1. **كلمات المرور (محمية بالكامل)**
- **التشفير:** استخدام `scrypt` عبر `generate_password_hash` من Werkzeug
- **التخزين:** مشفرة في قاعدة البيانات
- **الوصول:** لا يمكن لأي شخص رؤية كلمة المرور الأصلية
- **التحقق:** عبر `check_password_hash` فقط

### 2. **البيانات الشخصية (حماية متدرجة)**
- **أسماء المستخدمين:** إخفاء جزئي حسب الصلاحيات
- **البريد الإلكتروني:** إخفاء جزئي حسب الصلاحيات  
- **أرقام الهواتف:** إخفاء جزئي حسب الصلاحيات
- **معلومات الولايات:** مرئية للجميع (غير حساسة)

## 🛡️ قواعد الوصول للبيانات

### **الأدمن (Admin)**
- ✅ **يرى جميع البيانات كاملة:** أسماء المستخدمين، البريد الإلكتروني، أرقام الهواتف
- ✅ **صلاحية كاملة:** بدون قيود أو إخفاء
- 🔑 **المبرر:** الحاجة الإدارية الكاملة

### **مديرو المستخدمين (User Manager)**
- ✅ **يرون بيانات المفتشين والأساتذة كاملة:** للإدارة الفعالة
- ❌ **لا يرون بيانات الأدمن:** محظور أصلاً من الوصول
- 🔑 **المبرر:** الحاجة الإدارية المحدودة

### **المفتشون والأساتذة**
- ❌ **لا يمكنهم الوصول:** لقائمة المستخدمين أصلاً
- ✅ **يرون بياناتهم الشخصية كاملة:** في ملفهم الشخصي فقط
- 🔑 **المبرر:** الخصوصية والأمان

## 🎭 أنماط الإخفاء المطبقة

### 1. **أسماء المستخدمين**
```python
@property
def masked_username(self):
    if len(self.username) <= 3:
        return self.username
    elif len(self.username) <= 6:
        return self.username[:2] + '*' * (len(self.username) - 3) + self.username[-1:]
    else:
        return self.username[:3] + '*' * (len(self.username) - 6) + self.username[-3:]
```

**أمثلة:**
- `admin` → `admin` (قصير جداً)
- `teacher` → `te***r` (متوسط)
- `inspector123` → `ins****123` (طويل)

### 2. **البريد الإلكتروني**
```python
@property
def masked_email(self):
    if '@' in self.email:
        local, domain = self.email.split('@', 1)
        if len(local) <= 2:
            masked_local = local
        elif len(local) <= 4:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        else:
            masked_local = local[:2] + '*' * (len(local) - 4) + local[-2:]
        return f"{masked_local}@{domain}"
```

**أمثلة:**
- `<EMAIL>` → `ad***<EMAIL>`
- `<EMAIL>` → `te****<EMAIL>`
- `<EMAIL>` → `<EMAIL>` (قصير جداً)

### 3. **أرقام الهواتف**
```python
@property
def masked_phone(self):
    phone = self.phone_number.replace(' ', '').replace('-', '')
    if len(phone) == 10 and phone.startswith('0'):
        return f"{phone[:4]}****{phone[-2:]}"
    return phone[:3] + '*' * (len(phone) - 5) + phone[-2:] if len(phone) > 5 else phone
```

**أمثلة:**
- `0555123456` → `0555****56`
- `0661234567` → `0661****67`

## 🎨 المؤشرات البصرية

### **أيقونة الحماية**
- **الرمز:** 🛡️ (`fas fa-shield-alt`)
- **اللون:** تحذيري (`text-warning`)
- **الموضع:** بجانب البيانات المحمية
- **الرسالة:** "بيانات محمية" عند التمرير

### **التمييز البصري**
- **البيانات الكاملة:** عرض عادي
- **البيانات المحمية:** نص + أيقونة حماية
- **البيانات المحظورة:** أزرار معطلة + أيقونة منع

## 📊 مصفوفة الحماية الشاملة

| نوع البيانات | الأدمن | مدير المستخدمين | المفتش/الأستاذ |
|--------------|--------|------------------|-----------------|
| **كلمات المرور** | ❌ مشفرة | ❌ مشفرة | ❌ مشفرة |
| **اسم المستخدم (أدمن)** | ✅ كامل | 🛡️ مخفي | ❌ لا وصول |
| **اسم المستخدم (عادي)** | ✅ كامل | ✅ كامل | ❌ لا وصول |
| **البريد (أدمن)** | ✅ كامل | 🛡️ مخفي | ❌ لا وصول |
| **البريد (عادي)** | ✅ كامل | ✅ كامل | ❌ لا وصول |
| **الهاتف (أدمن)** | ✅ كامل | 🛡️ مخفي | ❌ لا وصول |
| **الهاتف (عادي)** | ✅ كامل | ✅ كامل | ❌ لا وصول |
| **الولاية** | ✅ كامل | ✅ كامل | ❌ لا وصول |

## 🔧 التطبيق التقني

### 1. **في النموذج (models_new.py)**
```python
# خصائص الإخفاء الجديدة
@property
def masked_username(self): ...

@property  
def masked_email(self): ...

@property
def masked_phone(self): ...
```

### 2. **في التطبيق (app.py)**
```python
# دالة التحكم في مستوى الحماية
def should_mask_user_data(viewer_role, target_user_role):
    if viewer_role == Role.ADMIN:
        return False
    if viewer_role == Role.USER_MANAGER:
        if target_user_role in [Role.TEACHER, Role.INSPECTOR]:
            return False
        return True
    return True
```

### 3. **في القوالب (Templates)**
```html
<!-- مثال للحماية المشروطة -->
{% if current_user.role == 'admin' or 
     (current_user.role == 'user_manager' and user.role != 'admin') %}
    {{ user.username }}
{% else %}
    {{ user.masked_username }}
    <i class="fas fa-shield-alt text-warning ms-1" title="بيانات محمية"></i>
{% endif %}
```

## 🛡️ الفوائد الأمنية

### 1. **حماية الخصوصية**
- منع تسريب البيانات الشخصية الكاملة
- تقليل المخاطر في حالة اختراق الحسابات
- حماية هوية المستخدمين الحساسة

### 2. **التحكم في الوصول**
- كل دور يرى ما يحتاجه فقط
- منع تسلسل الصلاحيات غير المرغوب
- حماية بيانات الأدمن من المستويات الأدنى

### 3. **الشفافية**
- المستخدمون يعرفون أن بياناتهم محمية
- مؤشرات بصرية واضحة للحماية
- توازن بين الوظائف والأمان

## 🔍 حالات الاختبار

### **اختبار 1: الأدمن يرى البيانات كاملة**
- ✅ **النتيجة المتوقعة:** جميع البيانات مرئية بوضوح
- ✅ **لا توجد أيقونات حماية:** للبيانات المرئية له

### **اختبار 2: مدير المستخدمين يرى بيانات الأساتذة**
- ✅ **النتيجة المتوقعة:** بيانات الأساتذة والمفتشين كاملة
- 🛡️ **بيانات الأدمن مخفية:** مع أيقونة الحماية

### **اختبار 3: محاولة الوصول المباشر**
- ❌ **النتيجة المتوقعة:** رفض الوصول للأدوار غير المصرحة
- 🔒 **الحماية:** على مستوى الخادم والواجهة

## 📈 التحسينات المستقبلية

### 1. **تشفير إضافي**
- تشفير أرقام الهواتف في قاعدة البيانات
- تشفير عناوين البريد الإلكتروني
- استخدام مفاتيح تشفير منفصلة

### 2. **سجلات الوصول**
- تسجيل من يصل لأي بيانات
- تنبيهات عند الوصول للبيانات الحساسة
- تقارير دورية عن الوصول

### 3. **حماية متقدمة**
- تشفير البيانات أثناء النقل (HTTPS)
- تشفير النسخ الاحتياطية
- آليات كشف التسريب

## 🔗 الملفات المحدثة

- **models_new.py:** إضافة خصائص الإخفاء
- **app.py:** دالة التحكم في مستوى الحماية
- **templates/users_list.html:** تطبيق الحماية في القائمة
- **templates/user_manager_dashboard.html:** تطبيق الحماية في اللوحة
- **templates/user_profile.html:** تطبيق الحماية في الملف الشخصي

---

**ملاحظة:** هذا النظام يوفر توازناً مثالياً بين الوظائف الإدارية المطلوبة وحماية خصوصية المستخدمين، مع ضمان أن كل دور يصل فقط للمعلومات التي يحتاجها لأداء مهامه.
