{% extends 'base.html' %}

{% block extra_css %}
<style>
    .collapse-icon {
        transition: transform 0.3s ease;
    }

    .notification-card {
        transition: all 0.3s ease;
    }

    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    .nav-tabs .nav-link {
        border-radius: 10px 10px 0 0;
        margin-right: 5px;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link.active {
        background-color: #007bff;
        color: white !important;
        border-color: #007bff;
    }

    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 10px 10px;
        padding: 20px;
        background-color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم الأستاذ</h2>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>نسبة الإنجاز</div>
                    <div class="h3">{{ (completion_rate|default(0))|round|int }}%</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#completionRateModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>مكتمل</div>
                    <div class="h3">{{ (progress_stats.completed|default(0)) }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#completedModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>قيد التنفيذ</div>
                    <div class="h3">{{ (progress_stats.in_progress|default(0)) }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#inProgressModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>مخطط</div>
                    <div class="h3">{{ (progress_stats.planned|default(0)) }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#plannedModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<!-- صندوق الإشعارات القابل للطي - أفقي على كامل الصفحة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-light" data-bs-toggle="collapse" data-bs-target="#notificationsCollapse"
                aria-expanded="false" aria-controls="notificationsCollapse" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-bell me-2 text-primary"></i>
                        <h5 class="mb-0 text-primary">آخر 3 إشعارات</h5>
                        {% if notifications and notifications|selectattr('is_read', 'eq', false)|list|length > 0 %}
                        <span class="badge bg-danger ms-3 pulse-animation">{{ notifications|selectattr('is_read', 'eq',
                            false)|list|length }}</span>
                        {% endif %}
                    </div>
                    <div class="d-flex align-items-center">
                        <small class="text-muted me-2">انقر للعرض/الإخفاء</small>
                        <i class="fas fa-chevron-down collapse-icon text-primary"></i>
                    </div>
                </div>
            </div>
            <div class="collapse" id="notificationsCollapse">
                <div class="card-body bg-light">
                    {% if notifications %}
                    <div class="row">
                        {% for notification in notifications %}
                        <div class="col-lg-6 col-xl-4 mb-3">
                            <div
                                class="card notification-card h-100 {% if not notification.is_read %}border-primary{% else %}border-light{% endif %}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6
                                            class="card-title {% if not notification.is_read %}text-primary fw-bold{% endif %}">
                                            {% if not notification.is_read %}
                                            <i class="fas fa-circle text-danger me-1" style="font-size: 0.5rem;"></i>
                                            {% endif %}
                                            {{ notification.title if notification.title else 'بدون عنوان' }}
                                        </h6>
                                        <small class="text-muted">{{ notification.created_at.strftime('%m-%d %H:%M') if
                                            notification.created_at else 'غير محدد' }}</small>
                                    </div>
                                    <p class="card-text small">{{ (notification.content[:100] if notification.content
                                        else 'لا يوجد محتوى') }}{% if notification.content and
                                        notification.content|length > 100 %}...{% endif %}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ notification.sender.name if
                                            notification.sender else 'مجهول' }}
                                        </small>
                                        {% if not notification.is_read %}
                                        <a href="{{ url_for('mark_notification_read', notification_type='inspector_teacher', notification_id=notification.id) }}"
                                            class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-check me-1"></i> تم القراءة
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-bell-slash fa-3x mb-3 text-muted"></i>
                        <h5 class="text-muted">لا توجد إشعارات جديدة</h5>
                        <p class="text-muted">ستظهر الإشعارات الجديدة هنا عند وصولها</p>
                    </div>
                    {% endif %}

                    <div class="d-flex justify-content-center mt-4">
                        <a href="{{ url_for('view_notifications') }}" class="btn btn-primary">
                            <i class="fas fa-bell me-2"></i> عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .pulse-animation {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .card-header:hover {
        background-color: #e3f2fd !important;
    }
</style>

<div class="row">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt me-1"></i>
                        جدول التدريس الأسبوعي
                    </div>
                    <div class="card-body">
                        <!-- علامات التبويب للأيام -->
                        <ul class="nav nav-tabs" id="weekDaysTabs" role="tablist">
                            {% set days_order = [6, 0, 1, 2, 3, 4, 5] %} <!-- الأحد أولاً -->
                            {% set days = ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'] %}

                            {% for day_index in days_order %}
                            <li class="nav-item" role="presentation">
                                <button class="nav-link {{ 'active' if loop.first else '' }}"
                                    id="day-{{ day_index }}-tab" data-bs-toggle="tab"
                                    data-bs-target="#day-{{ day_index }}" type="button" role="tab"
                                    aria-controls="day-{{ day_index }}"
                                    aria-selected="{{ 'true' if loop.first else 'false' }}">
                                    {{ days[day_index] }}
                                </button>
                            </li>
                            {% endfor %}
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content pt-3" id="weekDaysTabsContent">
                            {% for day_index in days_order %}
                            <div class="tab-pane fade {{ 'show active' if loop.first else '' }}"
                                id="day-{{ day_index }}" role="tabpanel" aria-labelledby="day-{{ day_index }}-tab">

                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th
                                                    style="color: black !important; background-color: #f8f9fa !important;">
                                                    الوقت</th>
                                                <th
                                                    style="color: black !important; background-color: #f8f9fa !important;">
                                                    المستوى</th>
                                                <th
                                                    style="color: black !important; background-color: #f8f9fa !important;">
                                                    المادة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% set day_schedules = schedules|selectattr('day_of_week', 'eq',
                                            day_index)|list %}
                                            {% if day_schedules %}
                                            {% for schedule in day_schedules %}
                                            <tr>
                                                <td>
                                                    {% if schedule.start_time and schedule.end_time %}
                                                    {{ schedule.start_time.strftime('%H:%M') }} - {{
                                                    schedule.end_time.strftime('%H:%M') }}
                                                    {% else %}
                                                    غير محدد
                                                    {% endif %}
                                                </td>
                                                <td>{{ schedule.level.name if schedule.level else 'غير محدد' }}</td>
                                                <td>{{ schedule.subject.name if schedule.subject else 'غير محدد' }}</td>
                                            </tr>
                                            {% endfor %}
                                            {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">لا توجد حصص مجدولة لهذا اليوم</td>
                                            </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                            <a href="{{ url_for('manage_schedule') }}" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i> تعديل الجدول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول آخر تحديثات التقدم - تم نقله إلى أعلى -->
<div class="row">


    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-chart-pie me-1"></i>
                            ملخص التقدم
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                يعرض هذا القسم إجمالي الموارد المعرفية في جميع المستويات التعليمية بالنظام
                            </small>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h6>
                                        <i class="fas fa-chart-line animated-icon me-2"></i>
                                        نسبة الإنجاز الإجمالية (بناءً على الموارد المعرفية)
                                    </h6>
                                    <div class="progress mb-2" style="height: 25px;">
                                        <div class="progress-bar bg-success" role="progressbar"
                                            data-completion-rate="{{ (completion_rate|default(0))|round|int }}"
                                            aria-valuemin="0" aria-valuemax="100">{{
                                            (completion_rate|default(0))|round|int }}%</div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                موارد معرفية مكتملة
                                            </span>
                                            <span>{{ progress_stats.completed_materials|default(0) }}</span>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-book me-1"></i>
                                                إجمالي الموارد المعرفية في النظام
                                            </span>
                                            <span>{{ progress_stats.total_materials|default(0) }} مادة معرفية</span>
                                        </div>
                                    </div>

                                    <!-- معلومات إضافية عن المواد التي يدرسها المعلم -->
                                    {% if progress_stats.teacher_total_materials is defined and
                                    progress_stats.teacher_total_materials > 0 %}
                                    <div
                                        class="mt-3 p-3 bg-info bg-opacity-10 rounded border border-info border-opacity-25">
                                        <h6 class="text-info mb-2">
                                            <i class="fas fa-graduation-cap me-1"></i>
                                            تقدمك في جميع الموارد المعرفية:
                                        </h6>
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="text-info fw-bold counter-number"
                                                    data-target="{{ progress_stats.completed_materials|default(0) }}">0
                                                </div>
                                                <small class="text-muted">مكتملة</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-info fw-bold counter-number"
                                                    data-target="{{ progress_stats.total_materials|default(0) }}">0
                                                </div>
                                                <small class="text-muted">إجمالي</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-info fw-bold counter-number"
                                                    data-target="{{ ((progress_stats.completed_materials|default(0) / progress_stats.total_materials|default(1)) * 100)|round|int }}">
                                                    0</div>
                                                <small class="text-muted">النسبة</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- قسم المواد التي يدرسها المعلم حسب جدوله -->
                                    {% if progress_stats.teacher_total_materials is defined and
                                    progress_stats.teacher_total_materials > 0 %}
                                    <div
                                        class="mt-3 p-3 bg-warning bg-opacity-10 rounded border border-warning border-opacity-25">
                                        <h6 class="text-warning mb-2">
                                            <i class="fas fa-chalkboard-teacher me-1"></i>
                                            الموارد المعرفية في جدولك الدراسي:
                                        </h6>
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="text-success fw-bold counter-number"
                                                    data-target="{{ progress_stats.completed|default(0) }}">0</div>
                                                <small class="text-muted">مكتملة</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-warning fw-bold counter-number"
                                                    data-target="{{ progress_stats.in_progress|default(0) }}">0</div>
                                                <small class="text-muted">قيد التنفيذ</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-info fw-bold counter-number"
                                                    data-target="{{ progress_stats.planned|default(0) }}">0</div>
                                                <small class="text-muted">مخططة</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- عرض التقدم بالطريقة القديمة للمقارنة -->
                                    <div class="mt-3 p-2 bg-light rounded">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            إحصائيات التقدم التقليدية:
                                        </small>
                                        <div class="d-flex justify-content-between mt-1">
                                            <div>
                                                <span class="badge bg-success">مكتمل</span> <span>{{
                                                    progress_stats.completed|default(0) }}</span>
                                            </div>
                                            <div>
                                                <span class="badge bg-warning">قيد التنفيذ</span> <span>{{
                                                    progress_stats.in_progress|default(0) }}</span>
                                            </div>
                                            <div>
                                                <span class="badge bg-danger">مخطط</span> <span>{{
                                                    progress_stats.planned|default(0) }}</span>
                                            </div>
                                            <div>
                                                <span class="badge bg-info">المجموع</span> <span>{{
                                                    progress_stats.total|default(0) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم التبويبات للمستويات التعليمية -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                <i class="fas fa-graduation-cap me-2"></i>
                                                التقدم التفصيلي حسب المستوى التعليمي
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- تبويبات المستويات -->
                                            <ul class="nav nav-tabs" id="levelTabs" role="tablist">
                                                {% for level_id, stats in level_stats.items() %}
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link {% if loop.first %}active{% endif %}"
                                                        id="level-{{ level_id }}-tab" data-bs-toggle="tab"
                                                        data-bs-target="#level-{{ level_id }}" type="button" role="tab"
                                                        aria-controls="level-{{ level_id }}"
                                                        aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                                        {{ stats.name }}
                                                        <span class="badge bg-primary ms-2">{{
                                                            (stats.completion_rate|default(0))|round|int }}%</span>
                                                    </button>
                                                </li>
                                                {% endfor %}
                                            </ul>

                                            <!-- محتوى التبويبات -->
                                            <div class="tab-content mt-3" id="levelTabsContent">
                                                {% for level_id, stats in level_stats.items() %}
                                                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                                                    id="level-{{ level_id }}" role="tabpanel"
                                                    aria-labelledby="level-{{ level_id }}-tab">

                                                    <!-- إحصائيات المستوى -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <div class="h3 text-primary">{{
                                                                    (stats.completion_rate|default(0))|round|int }}%
                                                                </div>
                                                                <small class="text-muted">نسبة الإنجاز</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <div class="h3 text-success">{{
                                                                    stats.completed_materials|default(0) }}</div>
                                                                <small class="text-muted">موارد مكتملة</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <div class="h3 text-info">{{
                                                                    stats.total_materials|default(0) }}</div>
                                                                <small class="text-muted">إجمالي الموارد</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <div class="h3 text-warning">{{
                                                                    (stats.total_materials|default(0)) -
                                                                    (stats.completed_materials|default(0)) }}</div>
                                                                <small class="text-muted">موارد متبقية</small>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- شريط التقدم للمستوى -->
                                                    <div class="progress mb-3" style="height: 25px;">
                                                        <div class="progress-bar bg-success" role="progressbar"
                                                            data-completion-rate="{{ (stats.completion_rate|default(0))|round|int }}"
                                                            aria-valuemin="0" aria-valuemax="100">
                                                            {{ (stats.completion_rate|default(0))|round|int }}%
                                                        </div>
                                                    </div>

                                                    <!-- التقدم حسب المادة في هذا المستوى -->
                                                    {% if stats.completed_by_subject %}
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-striped">
                                                            <thead>
                                                                <tr style="color: black;">
                                                                    <th style="color: black;">المادة الدراسية</th>
                                                                    <th style="color: black;">موارد مكتملة</th>
                                                                    <th style="color: black;">إجمالي الموارد</th>
                                                                    <th style="color: black;">النسبة المئوية</th>
                                                                    <th style="color: black;">التقدم</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {% for subject_name, completed in
                                                                stats.completed_by_subject.items() %}
                                                                {% set total = stats.total_by_subject.get(subject_name,
                                                                0) %}
                                                                {% set percentage = ((completed / total * 100) if total
                                                                > 0 else 0)|round|int %}
                                                                <tr>
                                                                    <td>
                                                                        <i class="fas fa-book me-1 text-primary"></i>
                                                                        {{ subject_name }}
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge bg-success">{{ completed
                                                                            }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge bg-primary">{{ total
                                                                            }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <strong class="text-primary">{{ percentage
                                                                            }}%</strong>
                                                                    </td>
                                                                    <td>
                                                                        <div class="progress"
                                                                            style="height: 20px; width: 100px;">
                                                                            <div class="progress-bar
                                                                            {% if percentage >= 80 %}bg-success
                                                                            {% elif percentage >= 60 %}bg-warning
                                                                            {% elif percentage >= 40 %}bg-info
                                                                            {% else %}bg-danger
                                                                            {% endif %}" role="progressbar"
                                                                                data-percentage="{{ percentage }}"
                                                                                aria-valuemin="0" aria-valuemax="100">
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    {% else %}
                                                    <div class="alert alert-info">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        لا توجد بيانات تقدم متاحة لهذا المستوى حالياً
                                                        <br>
                                                        <small class="text-muted">
                                                            يمكنك البدء بتسجيل التقدم من صفحة
                                                            <a href="{{ url_for('teaching_program') }}"
                                                                class="alert-link">البرنامج التعليمي</a>
                                                        </small>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>




                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-tasks me-1"></i>
                            آخر تحديثات التقدم
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('teaching_program') }}" class="btn btn-sm btn-success">
                                <i class="fas fa-plus me-1"></i> إضافة تقدم جديد
                            </a>
                            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse"
                                data-bs-target="#filterOptions" aria-expanded="false" aria-controls="filterOptions">
                                <i class="fas fa-filter me-1"></i> خيارات التصفية
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="collapse mb-3" id="filterOptions">
                        <div class="card card-body bg-light">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <label for="filterLevel" class="form-label">
                                        <i class="fas fa-school me-1"></i> المستوى
                                    </label>
                                    <select class="form-select form-select-sm" id="filterLevel">
                                        <option value="">الكل</option>
                                        {% for level_id, stats in level_stats.items() %}
                                        <option value="{{ level_id }}">{{ stats.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label for="filterSubject" class="form-label">
                                        <i class="fas fa-book me-1"></i> المادة
                                    </label>
                                    <select class="form-select form-select-sm" id="filterSubject">
                                        <option value="">الكل</option>
                                        {% for subject_id, stats in subject_stats.items() %}
                                        <option value="{{ subject_id }}"
                                            data-level="{{ stats.level_id if stats.level_id else '' }}">
                                            {{ stats.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label for="filterStatus" class="form-label">
                                        <i class="fas fa-tasks me-1"></i> الحالة
                                    </label>
                                    <select class="form-select form-select-sm" id="filterStatus">
                                        <option value="">الكل</option>
                                        <option value="completed">مكتمل</option>
                                        <option value="in_progress">قيد التنفيذ</option>
                                        <option value="planned">مخطط</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label for="sortBy" class="form-label">
                                        <i class="fas fa-sort me-1"></i> ترتيب حسب
                                    </label>
                                    <select class="form-select form-select-sm" id="sortBy">
                                        <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                                        <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                                        <option value="status">الحالة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-3">
                                <button class="btn btn-sm btn-primary me-2" id="applyFilters">
                                    <i class="fas fa-filter me-1"></i> تطبيق التصفية
                                </button>
                                <button class="btn btn-sm btn-secondary" id="resetFilters">
                                    <i class="fas fa-undo me-1"></i> إعادة ضبط
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظة حول عرض التقدمات -->
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>ملاحظة:</strong> يعرض الجدول أدناه جميع تقدماتك. استخدم شريط التمرير للاطلاع على
                        التقدمات الأقدم.
                    </div>

                    <div class="table-responsive"
                        style="max-height: 600px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                        <table class="table table-bordered table-hover" id="progressTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center"
                                        style="width: 100px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-calendar-day me-1"></i> التاريخ
                                    </th>
                                    <th class="text-center"
                                        style="width: 120px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-school me-1"></i> المستوى
                                    </th>
                                    <th class="text-center"
                                        style="width: 120px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-book me-1"></i> المادة
                                    </th>
                                    <th class="text-center"
                                        style="width: 120px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-sitemap me-1"></i> الميدان
                                    </th>
                                    <th class="text-center"
                                        style="width: 150px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-bookmark me-1"></i> المادة المعرفية
                                    </th>
                                    <th class="text-center"
                                        style="color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-graduation-cap me-1"></i> الكفاءة
                                    </th>
                                    <th class="text-center"
                                        style="width: 100px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-tasks me-1"></i> الحالة
                                    </th>
                                    <th class="text-center"
                                        style="width: 120px; color: black !important; background-color: #f8f9fa !important;">
                                        <i class="fas fa-chalkboard me-1"></i> الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if progress_entries %}
                                {% for entry in progress_entries %}
                                <tr data-level="{{ entry.level.id if entry.level else '' }}"
                                    data-subject="{{ entry.subject.id if entry.subject else '' }}"
                                    data-status="{{ entry.status }}"
                                    class="{% if entry.status == 'completed' %}table-success{% elif entry.status == 'in_progress' %}table-warning{% elif entry.status == 'planned' %}table-danger{% endif %}">
                                    <td class="text-center">{{ entry.date.strftime('%Y-%m-%d') if entry.date else '' }}
                                    </td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>
                                        {% if entry.competency %}
                                        <div class="competency-text"
                                            title="{% if entry.competency.name %}{{ entry.competency.name }}{% elif entry.competency.description %}{{ entry.competency.description }}{% else %}بدون اسم{% endif %}">
                                            {% if entry.competency.name %}
                                            {{ entry.competency.name[:80] }}{{ '...' if entry.competency.name|length >
                                            80 else '' }}
                                            {% elif entry.competency.description %}
                                            {{ entry.competency.description[:80] }}{{ '...' if
                                            entry.competency.description|length > 80 else '' }}
                                            {% else %}
                                            <em>بدون اسم</em>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <em>كفاءة غير معروفة</em>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if entry.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif entry.status == 'in_progress' %}
                                        <span class="badge bg-warning">قيد التنفيذ</span>
                                        {% else %}
                                        <span class="badge bg-danger">مخطط</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}"
                                                class="btn btn-sm btn-outline-primary" target="_blank"
                                                title="تحضير خطة درس">
                                                <i class="fas fa-chalkboard-teacher"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-sm btn-outline-warning" title="تعديل التقدم">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-sm btn-outline-danger" title="حذف التقدم"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد سجلات تقدم حتى الآن</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- مؤشر للتمرير إذا كان هناك المزيد من التقدمات -->
                    {% if progress_entries|length > 10 %}
                    <div class="text-center mt-2 mb-3">
                        <small class="text-muted">
                            <i class="fas fa-arrow-down me-1"></i>
                            يوجد {{ progress_entries|length - 10 }} تقدم إضافي أسفل الجدول - استخدم شريط التمرير
                            <i class="fas fa-arrow-down ms-1"></i>
                        </small>
                    </div>
                    {% endif %}
                    <style>
                        .competency-text {
                            max-width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            cursor: help;
                        }

                        /* تحسين مظهر شريط التمرير */
                        .table-responsive::-webkit-scrollbar {
                            width: 8px;
                            height: 8px;
                        }

                        .table-responsive::-webkit-scrollbar-track {
                            background: #f1f1f1;
                            border-radius: 4px;
                        }

                        .table-responsive::-webkit-scrollbar-thumb {
                            background: #888;
                            border-radius: 4px;
                        }

                        .table-responsive::-webkit-scrollbar-thumb:hover {
                            background: #555;
                        }
                    </style>
                    <!-- أزرار التصدير والطباعة -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-download me-1"></i>
                                        تصدير وطباعة التقدمات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- تصدير إلى Excel -->
                                        <div class="col-md-6">
                                            <form method="POST" action="{{ url_for('export_progress_excel') }}"
                                                class="d-inline">
                                                <div class="mb-3">
                                                    <label for="export_date" class="form-label">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        اختر التاريخ للتصدير إلى Excel:
                                                    </label>
                                                    <input type="date" class="form-control" id="export_date"
                                                        name="export_date" required>
                                                </div>
                                                <button type="submit" class="btn btn-success w-100">
                                                    <i class="fas fa-file-excel me-1"></i>
                                                    تصدير إلى Excel
                                                </button>
                                            </form>
                                        </div>

                                        <!-- طباعة -->
                                        <div class="col-md-6">
                                            <form method="POST" action="{{ url_for('print_progress') }}"
                                                class="d-inline" target="_blank">
                                                <div class="mb-3">
                                                    <label for="print_date" class="form-label">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        اختر التاريخ للطباعة:
                                                    </label>
                                                    <input type="date" class="form-control" id="print_date"
                                                        name="print_date" required>
                                                </div>
                                                <button type="submit" class="btn btn-info w-100">
                                                    <i class="fas fa-print me-1"></i>
                                                    طباعة التقدمات
                                                </button>
                                            </form>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <strong>ملاحظة:</strong> سيتم تصدير/طباعة جميع التقدمات المسجلة في
                                                التاريخ المحدد فقط.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                        <a href="{{ url_for('teaching_program') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إضافة تقدم جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    {% block extra_js %}

    <!-- HTML comment to hide Jinja2 template code from IDE JavaScript parser -->


    <script>
        /* eslint-disable */
        // @ts-nocheck
        document.addEventListener('DOMContentLoaded', function () {
            // تعيين عرض أشرطة التقدم
            document.querySelectorAll('.progress-bar[data-completion-rate]').forEach(function (bar) {
                const rate = bar.getAttribute('data-completion-rate');
                bar.style.width = rate + '%';
            });

            document.querySelectorAll('.progress-bar[data-percentage]').forEach(function (bar) {
                const percentage = bar.getAttribute('data-percentage');
                bar.style.width = percentage + '%';
            });

            // تبسيط الفلترة - لا نحتاج البيانات المعقدة
            console.log('تم تحميل صفحة لوحة التحكم');

            // وظائف التصفية والترتيب
            const filterLevel = document.getElementById('filterLevel');
            const filterSubject = document.getElementById('filterSubject');
            const filterStatus = document.getElementById('filterStatus');
            const sortBy = document.getElementById('sortBy');
            const applyFilters = document.getElementById('applyFilters');
            const resetFilters = document.getElementById('resetFilters');
            const progressTable = document.getElementById('progressTable');

            // تحديث قائمة المواد بناءً على المستوى المحدد
            filterLevel.addEventListener('change', function () {
                const selectedLevel = this.value;
                const subjectOptions = filterSubject.querySelectorAll('option');

                // إعادة تعيين قائمة المواد
                filterSubject.value = '';

                // إظهار أو إخفاء المواد بناءً على المستوى
                subjectOptions.forEach(option => {
                    if (option.value === '') {
                        // دائمًا إظهار خيار "الكل"
                        option.style.display = '';
                    } else {
                        const optionLevel = option.getAttribute('data-level');
                        if (!selectedLevel || selectedLevel === '' || optionLevel === selectedLevel) {
                            option.style.display = '';
                        } else {
                            option.style.display = 'none';
                        }
                    }
                });
            });

            // تطبيق التصفية
            function applyFiltering() {
                const levelFilter = filterLevel.value;
                const subjectFilter = filterSubject.value;
                const statusFilter = filterStatus.value;
                const sortOption = sortBy.value;

                // الحصول على جميع الصفوف
                const rows = progressTable.querySelectorAll('tbody tr');

                // تصفية الصفوف
                rows.forEach(row => {
                    const rowLevel = row.getAttribute('data-level');
                    const rowSubject = row.getAttribute('data-subject');
                    const rowStatus = row.getAttribute('data-status');

                    let showRow = true;

                    // طباعة قيم التصفية للتصحيح
                    console.log('Filter values:', { levelFilter, rowLevel, subjectFilter, rowSubject, statusFilter, rowStatus });

                    // تطبيق التصفية حسب المستوى
                    if (levelFilter && levelFilter !== '' && rowLevel !== levelFilter) {
                        showRow = false;
                    }

                    // تطبيق التصفية حسب المادة
                    if (showRow && subjectFilter && subjectFilter !== '' && rowSubject !== subjectFilter) {
                        showRow = false;
                    }

                    // تطبيق التصفية حسب الحالة
                    if (showRow && statusFilter && statusFilter !== '' && rowStatus !== statusFilter) {
                        showRow = false;
                    }

                    // إظهار أو إخفاء الصف
                    row.style.display = showRow ? '' : 'none';
                });

                // ترتيب الصفوف
                const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');

                if (visibleRows.length > 0) {
                    // ترتيب حسب الخيار المحدد
                    visibleRows.sort((a, b) => {
                        if (sortOption === 'date_desc') {
                            return new Date(b.cells[0].textContent) - new Date(a.cells[0].textContent);
                        } else if (sortOption === 'date_asc') {
                            return new Date(a.cells[0].textContent) - new Date(b.cells[0].textContent);
                        } else if (sortOption === 'status') {
                            const statusOrder = { 'completed': 1, 'in_progress': 2, 'planned': 3 };
                            const statusA = a.getAttribute('data-status');
                            const statusB = b.getAttribute('data-status');
                            return statusOrder[statusA] - statusOrder[statusB];
                        }
                        return 0;
                    });

                    // إعادة ترتيب الصفوف في الجدول
                    const tbody = progressTable.querySelector('tbody');
                    visibleRows.forEach(row => tbody.appendChild(row));

                    // إظهار رسالة إذا لم تكن هناك نتائج
                    const noResultsRow = progressTable.querySelector('tr[data-no-results]');
                    if (noResultsRow) {
                        noResultsRow.style.display = 'none';
                    }
                } else {
                    // إظهار رسالة إذا لم تكن هناك نتائج
                    let noResultsRow = progressTable.querySelector('tr[data-no-results]');
                    if (!noResultsRow) {
                        const tbody = progressTable.querySelector('tbody');
                        noResultsRow = document.createElement('tr');
                        noResultsRow.setAttribute('data-no-results', 'true');
                        const cell = document.createElement('td');
                        cell.setAttribute('colspan', '8');
                        cell.classList.add('text-center');
                        cell.textContent = 'لا توجد نتائج مطابقة للتصفية';
                        noResultsRow.appendChild(cell);
                        tbody.appendChild(noResultsRow);
                    } else {
                        noResultsRow.style.display = '';
                    }
                }
            }

            // إعادة ضبط التصفية
            function resetFiltering() {
                filterLevel.value = '';
                filterSubject.value = '';
                filterStatus.value = '';
                sortBy.value = 'date_desc';

                // إظهار جميع الصفوف
                const rows = progressTable.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    if (!row.hasAttribute('data-no-results')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                // إعادة ترتيب الصفوف حسب التاريخ (الأحدث أولاً)
                const visibleRows = Array.from(rows).filter(row => !row.hasAttribute('data-no-results'));
                visibleRows.sort((a, b) => new Date(b.cells[0].textContent) - new Date(a.cells[0].textContent));

                const tbody = progressTable.querySelector('tbody');
                visibleRows.forEach(row => tbody.appendChild(row));
            }

            // إضافة مستمعي الأحداث
            applyFilters.addEventListener('click', applyFiltering);
            resetFilters.addEventListener('click', resetFiltering);





            // تم إزالة معالجات الحذف المعقدة

            // تحديث أيقونات الطي للإشعارات
            document.getElementById('notificationsCollapse').addEventListener('show.bs.collapse', function () {
                const icon = document.querySelector('[data-bs-target="#notificationsCollapse"] .collapse-icon');
                if (icon) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                }
            });

            document.getElementById('notificationsCollapse').addEventListener('hide.bs.collapse', function () {
                const icon = document.querySelector('[data-bs-target="#notificationsCollapse"] .collapse-icon');
                if (icon) {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            });

            // تحسين التبويبات للمستويات التعليمية
            document.querySelectorAll('#levelTabs button[data-bs-toggle="tab"]').forEach(function (tabButton) {
                tabButton.addEventListener('shown.bs.tab', function (event) {
                    // إضافة تأثير بصري عند تغيير التبويب
                    const targetPane = document.querySelector(event.target.getAttribute('data-bs-target'));
                    if (targetPane) {
                        targetPane.style.opacity = '0';
                        setTimeout(function () {
                            targetPane.style.transition = 'opacity 0.3s ease';
                            targetPane.style.opacity = '1';
                        }, 50);
                    }
                });
            });

            // تحديث أشرطة التقدم في التبويبات
            function updateTabProgressBars() {
                document.querySelectorAll('.tab-pane .progress-bar[data-percentage]').forEach(function (bar) {
                    const percentage = bar.getAttribute('data-percentage');
                    setTimeout(function () {
                        bar.style.width = percentage + '%';
                    }, 100);
                });
            }

            // تشغيل تحديث أشرطة التقدم عند تحميل الصفحة
            updateTabProgressBars();

            // تشغيل تحديث أشرطة التقدم عند تغيير التبويب
            document.querySelectorAll('#levelTabs button[data-bs-toggle="tab"]').forEach(function (tabButton) {
                tabButton.addEventListener('shown.bs.tab', function () {
                    updateTabProgressBars();
                });
            });

            // تحسين تجربة المستخدم لأزرار التصدير والطباعة
            const exportForm = document.querySelector('form[action="{{ url_for("export_progress_excel") }}"]');
            const printForm = document.querySelector('form[action="{{ url_for("print_progress") }}"]');

            if (exportForm) {
                exportForm.addEventListener('submit', function (e) {
                    const dateInput = this.querySelector('input[name="export_date"]');
                    if (!dateInput.value) {
                        e.preventDefault();
                        alert('يرجى تحديد تاريخ للتصدير');
                        dateInput.focus();
                        return false;
                    }

                    // إظهار رسالة تحميل
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التصدير...';
                    submitBtn.disabled = true;

                    // إعادة تفعيل الزر بعد 3 ثوان
                    setTimeout(function () {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                });
            }

            if (printForm) {
                printForm.addEventListener('submit', function (e) {
                    const dateInput = this.querySelector('input[name="print_date"]');
                    if (!dateInput.value) {
                        e.preventDefault();
                        alert('يرجى تحديد تاريخ للطباعة');
                        dateInput.focus();
                        return false;
                    }

                    // إظهار رسالة تحميل
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحضير...';
                    submitBtn.disabled = true;

                    // إعادة تفعيل الزر بعد 2 ثانية
                    setTimeout(function () {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 2000);
                });
            }

            // تعيين التاريخ الحالي كقيمة افتراضية
            const today = new Date().toISOString().split('T')[0];
            const exportDateInput = document.getElementById('export_date');
            const printDateInput = document.getElementById('print_date');

            if (exportDateInput) {
                exportDateInput.value = today;
            }
            if (printDateInput) {
                printDateInput.value = today;
            }

            // تحسين تجربة التمرير في جدول التقدمات
            const progressTableContainer = document.querySelector('.table-responsive');
            if (progressTableContainer) {
                // إضافة مؤشر تمرير
                const scrollIndicator = document.createElement('div');
                scrollIndicator.className = 'scroll-indicator';
                scrollIndicator.innerHTML = '<i class="fas fa-chevron-down"></i>';
                scrollIndicator.style.cssText = `
                    position: absolute;
                    bottom: 10px;
                    right: 20px;
                    background: rgba(0,123,255,0.8);
                    color: white;
                    padding: 5px 8px;
                    border-radius: 15px;
                    font-size: 12px;
                    display: none;
                    z-index: 10;
                `;

                progressTableContainer.style.position = 'relative';
                progressTableContainer.appendChild(scrollIndicator);

                // إظهار/إخفاء مؤشر التمرير
                progressTableContainer.addEventListener('scroll', function () {
                    const isScrollable = this.scrollHeight > this.clientHeight;
                    const isAtBottom = this.scrollTop + this.clientHeight >= this.scrollHeight - 5;

                    if (isScrollable && !isAtBottom) {
                        scrollIndicator.style.display = 'block';
                    } else {
                        scrollIndicator.style.display = 'none';
                    }
                });

                // فحص أولي لإظهار المؤشر إذا كان المحتوى قابل للتمرير
                setTimeout(() => {
                    const isScrollable = progressTableContainer.scrollHeight > progressTableContainer.clientHeight;
                    if (isScrollable) {
                        scrollIndicator.style.display = 'block';
                    }
                }, 100);
            }
        });
    </script>
    {% endblock %}



    <!-- Modal for Completion Rate Details -->
    <div class="modal fade" id="completionRateModal" tabindex="-1" aria-labelledby="completionRateModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="completionRateModalLabel">تفاصيل نسبة الإنجاز</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>نسبة الإنجاز الإجمالية: {{ (completion_rate|default(0))|round|int }}%</h5>
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                    data-completion-rate="{{ (completion_rate|default(0))|round|int }}"
                                    aria-valuenow="{{ (completion_rate|default(0))|round|int }}" aria-valuemin="0"
                                    aria-valuemax="100">{{ (completion_rate|default(0))|round|int }}%</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-graduation-cap animated-icon me-2"></i>
                                    نسبة الإنجاز حسب المستوى (بناءً على الموارد المعرفية)
                                </h5>
                                <a href="{{ url_for('recalculate_stats') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    إعادة حساب الإحصائيات
                                </a>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المستوى</th>
                                            <th>نسبة الإنجاز</th>
                                            <th>موارد معرفية مكتملة</th>
                                            <th>إجمالي الموارد المعرفية</th>
                                            <th>التقدم التقليدي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for level_id, stats in level_stats.items() %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-school me-1 text-primary"></i>
                                                {{ stats.name }}
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 25px;">
                                                    <div class="progress-bar
                                                    {% set completion_rate_val = (stats.completion_rate|default(0)) %}
                                                    {% if completion_rate_val >= 80 %}bg-success
                                                    {% elif completion_rate_val >= 60 %}bg-warning
                                                    {% elif completion_rate_val >= 40 %}bg-info
                                                    {% else %}bg-danger
                                                    {% endif %}" role="progressbar"
                                                        data-completion-rate="{{ completion_rate_val|round|int }}"
                                                        aria-valuenow="{{ completion_rate_val|round|int }}"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        {{ completion_rate_val|round|int }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ stats.completed_materials or 0
                                                    }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ stats.total_materials or 0 }}</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    مكتمل: {{ stats.completed|default(0) }},
                                                    قيد التنفيذ: {{ stats.in_progress|default(0) }},
                                                    مخطط: {{ stats.planned|default(0) }},
                                                    المجموع: {{ stats.total|default(0) }}
                                                </small>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5>نسبة الإنجاز حسب المادة</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                المادة</th>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                نسبة الإنجاز</th>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                مكتمل</th>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                قيد التنفيذ</th>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                مخطط</th>
                                            <th style="color: black !important; background-color: #f8f9fa !important;">
                                                المجموع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for subject_id, stats in subject_stats.items() %}
                                        <tr>
                                            <td>{{ stats.name }}</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar"
                                                        data-completion-rate="{{ (stats.completion_rate|default(0))|round|int }}"
                                                        aria-valuenow="{{ (stats.completion_rate|default(0))|round|int }}"
                                                        aria-valuemin="0" aria-valuemax="100">{{
                                                        (stats.completion_rate|default(0))|round|int }}%</div>
                                                </div>
                                            </td>
                                            <td>{{ stats.completed|default(0) }}</td>
                                            <td>{{ stats.in_progress|default(0) }}</td>
                                            <td>{{ stats.planned|default(0) }}</td>
                                            <td>{{ stats.total|default(0) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Completed Tasks -->
    <div class="modal fade" id="completedModal" tabindex="-1" aria-labelledby="completedModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="completedModalLabel">المهام المكتملة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">التاريخ
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المستوى
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الميدان
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                        المعرفية</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الكفاءة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set completed_entries = progress_entries|selectattr('status', 'eq', 'completed')|list
                                %}
                                {% if completed_entries %}
                                {% for entry in completed_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.name if entry.competency and entry.competency.name else
                                        (entry.competency.description if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}"
                                                class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام مكتملة</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for In Progress Tasks -->
    <div class="modal fade" id="inProgressModal" tabindex="-1" aria-labelledby="inProgressModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="inProgressModalLabel">المهام قيد التنفيذ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">التاريخ
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المستوى
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الميدان
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                        المعرفية</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الكفاءة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set in_progress_entries = progress_entries|selectattr('status', 'eq',
                                'in_progress')|list %}
                                {% if in_progress_entries %}
                                {% for entry in in_progress_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.name if entry.competency and entry.competency.name else
                                        (entry.competency.description if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}"
                                                class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام قيد التنفيذ</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Planned Tasks -->
    <div class="modal fade" id="plannedModal" tabindex="-1" aria-labelledby="plannedModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="plannedModalLabel">المهام المخططة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">التاريخ
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المستوى
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الميدان
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                        المعرفية</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الكفاءة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set planned_entries = progress_entries|selectattr('status', 'eq', 'planned')|list %}
                                {% if planned_entries %}
                                {% for entry in planned_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.name if entry.competency and entry.competency.name else
                                        (entry.competency.description if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}"
                                                class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام مخططة</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Planned Tasks -->
    <div class="modal fade" id="plannedModal" tabindex="-1" aria-labelledby="plannedModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="plannedModalLabel">المهام المخططة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">التاريخ
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المستوى
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الميدان
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المادة
                                        المعرفية</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الكفاءة
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set planned_entries = progress_entries|selectattr('status', 'eq', 'planned')|list %}
                                {% if planned_entries %}
                                {% for entry in planned_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.name if entry.competency and entry.competency.name else
                                        (entry.competency.description if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}"
                                                class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_progress', entry_id=entry.id) }}"
                                                class="btn btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام مخططة</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* تأثير العداد الدوار */
        .counter-number {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            transition: all 0.3s ease;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .counter-number.counting {
            animation: counterSpin 0.1s ease-in-out;
        }

        @keyframes counterSpin {
            0% {
                transform: rotateY(0deg) scale(1);
            }

            50% {
                transform: rotateY(180deg) scale(1.1);
            }

            100% {
                transform: rotateY(360deg) scale(1);
            }
        }

        /* تأثير إضافي عند التمرير */
        .counter-number.in-view {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // دالة العداد الدوار
            function animateCounter(element, target, duration = 2000) {
                const start = 0;
                const increment = target / (duration / 16); // 60 FPS
                let current = start;

                const timer = setInterval(() => {
                    current += increment;

                    // إضافة تأثير الدوران أثناء العد
                    element.classList.add('counting');
                    setTimeout(() => {
                        element.classList.remove('counting');
                    }, 100);

                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // عرض الرقم مع تأثير النسبة المئوية
                    if (element.textContent.includes('%') || element.dataset.target.includes('%')) {
                        element.textContent = Math.floor(current) + '%';
                    } else {
                        element.textContent = Math.floor(current);
                    }
                }, 16);
            }

            // مراقب التمرير للتفعيل عند الوصول للعنصر
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const target = parseInt(element.dataset.target);

                        // إضافة تأثير الظهور
                        element.classList.add('in-view');

                        // بدء العداد
                        setTimeout(() => {
                            animateCounter(element, target);
                        }, 200);

                        // إيقاف المراقبة بعد التفعيل
                        observer.unobserve(element);
                    }
                });
            }, observerOptions);

            // مراقبة جميع العدادات
            const counters = document.querySelectorAll('.counter-number');
            counters.forEach(counter => {
                observer.observe(counter);
            });
        });
    </script>

    {% endblock %}