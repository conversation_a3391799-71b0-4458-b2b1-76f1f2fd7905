{% extends "base.html" %}

{% block title %}إدارة الحسابات المهجورة{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- العنوان الرئيسي -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user-times me-2"></i>
                    إدارة الحسابات المهجورة
                </h2>
                <a href="{{ url_for('admin_advanced') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للإدارة المتقدمة
                </a>
            </div>

            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3>{{ stats.total_abandoned }}</h3>
                            <p class="mb-0">إجمالي الحسابات المهجورة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>{{ stats.never_logged_in }}</h3>
                            <p class="mb-0">لم تسجل دخول أبداً</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ stats.inactive_long_time }}</h3>
                            <p class="mb-0">غير نشطة لفترة طويلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3>{{ stats.teachers }}</h3>
                            <p class="mb-0">أساتذة مهجورة</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if abandoned_accounts %}
            <!-- نموذج حذف الحسابات -->
            <form method="POST" action="{{ url_for('delete_abandoned_accounts') }}" id="deleteForm">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الحسابات المهجورة (أكثر من 6 أشهر بدون نشاط)
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- أدوات التحكم -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                    <i class="fas fa-check-square me-1"></i>
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNone()">
                                    <i class="fas fa-square me-1"></i>
                                    إلغاء التحديد
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-danger" onclick="showDeleteModal()" id="deleteBtn" disabled>
                                    <i class="fas fa-trash me-2"></i>
                                    حذف المحدد
                                </button>
                            </div>
                        </div>

                        <!-- جدول الحسابات المهجورة -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                                        </th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>رقم الهاتف</th>
                                        <th>الولاية</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>آخر دخول</th>
                                        <th>أيام الخمول</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in abandoned_accounts %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_accounts" value="{{ user.id }}" 
                                                   class="account-checkbox" onchange="updateDeleteButton()">
                                        </td>
                                        <td>
                                            <strong>{{ user.username }}</strong>
                                        </td>
                                        <td>{{ user.email }}</td>
                                        <td>
                                            {% if user.role == 'admin' %}
                                                <span class="badge bg-danger">إدارة</span>
                                            {% elif user.role == 'inspector' %}
                                                <span class="badge bg-warning">مفتش</span>
                                            {% elif user.role == 'teacher' %}
                                                <span class="badge bg-info">أستاذ</span>
                                            {% elif user.role == 'user_manager' %}
                                                <span class="badge bg-secondary">مدير مستخدمين</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.formatted_phone or 'غير محدد' }}</td>
                                        <td>{{ user.wilaya_name or 'غير محدد' }}</td>
                                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if user.last_login %}
                                                {{ user.last_login.strftime('%Y-%m-%d') }}
                                            {% else %}
                                                <span class="text-muted">لم يسجل دخول أبداً</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">{{ user.days_since_last_login }} يوم</span>
                                        </td>
                                        <td>
                                            {% if user.is_active %}
                                                <span class="badge bg-success">مفعل</span>
                                            {% else %}
                                                <span class="badge bg-secondary">معطل</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>

            <!-- مودال تأكيد الحذف -->
            <div class="modal fade" id="deleteModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تأكيد حذف الحسابات المهجورة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها!
                            </div>
                            
                            <p>أنت على وشك حذف <span id="selectedCount" class="fw-bold text-danger"></span> حساب مهجور.</p>
                            <p>سيتم حذف جميع البيانات المرتبطة بهذه الحسابات نهائياً.</p>
                            
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">كلمة مرور الأدمن للتأكيد:</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirm_delete" name="confirm_delete" required>
                                <label class="form-check-label text-danger" for="confirm_delete">
                                    أؤكد أنني أريد حذف الحسابات المحددة نهائياً
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-danger" form="deleteForm">
                                <i class="fas fa-trash me-2"></i>
                                حذف نهائي
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {% else %}
            <!-- رسالة عدم وجود حسابات مهجورة -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3 text-success">ممتاز!</h3>
                    <p class="text-muted">لا توجد حسابات مهجورة في النظام</p>
                    <p class="text-muted">جميع الحسابات نشطة أو تم استخدامها خلال الـ 6 أشهر الماضية</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// تحديد/إلغاء تحديد جميع الحسابات
function selectAll() {
    const checkboxes = document.querySelectorAll('.account-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
    updateDeleteButton();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.account-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateDeleteButton();
}

function toggleAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.account-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
    updateDeleteButton();
}

// تحديث حالة زر الحذف
function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.account-checkbox:checked');
    const deleteBtn = document.getElementById('deleteBtn');
    deleteBtn.disabled = checkedBoxes.length === 0;
}

// إظهار مودال التأكيد
function showDeleteModal() {
    const checkedBoxes = document.querySelectorAll('.account-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('يرجى تحديد الحسابات المراد حذفها');
        return;
    }
    
    document.getElementById('selectedCount').textContent = checkedBoxes.length;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// إضافة كلمة المرور إلى النموذج عند الإرسال
document.getElementById('deleteForm').addEventListener('submit', function(e) {
    const password = document.getElementById('admin_password').value;
    const confirm = document.getElementById('confirm_delete').checked;
    
    if (!password || !confirm) {
        e.preventDefault();
        alert('يرجى إدخال كلمة المرور وتأكيد الموافقة');
        return;
    }
    
    // إضافة الحقول المخفية
    const passwordInput = document.createElement('input');
    passwordInput.type = 'hidden';
    passwordInput.name = 'admin_password';
    passwordInput.value = password;
    this.appendChild(passwordInput);
    
    const confirmInput = document.createElement('input');
    confirmInput.type = 'hidden';
    confirmInput.name = 'confirm_delete';
    confirmInput.value = 'true';
    this.appendChild(confirmInput);
});
</script>
{% endblock %}
