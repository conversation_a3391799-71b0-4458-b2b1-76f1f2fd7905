# ميزة عرض آخر نشاط في الملف الشخصي

## الوصف
تم إضافة ميزة عرض توقيت وتاريخ آخر نشاط في الموقع إلى صفحة الملف الشخصي للمستخدمين. هذه الميزة متاحة للأدمن ومدير المستخدمين لمراقبة نشاط المستخدمين.

## الميزات المضافة

### 1. معلومات آخر تسجيل دخول
- **التاريخ والوقت:** عرض تاريخ ووقت آخر تسجيل دخول بالتفصيل
- **عدد الأيام:** حساب عدد الأيام منذ آخر تسجيل دخول
- **مؤشرات بصرية:** استخدام ألوان مختلفة حسب مستوى النشاط

### 2. تقييم حالة النشاط
- **نشط اليوم:** مؤشر أخضر للمستخدمين النشطين
- **نشط خلال الأسبوع:** مؤشر أزرق للنشاط الحديث
- **غير نشط خلال الشهر:** مؤشر أصفر للتحذير
- **خامل (أكثر من شهر):** مؤشر أحمر للمستخدمين الخاملين
- **مهجور (أكثر من 6 أشهر):** مؤشر أسود للحسابات المهجورة

### 3. معلومات إضافية حسب الدور

#### للمعلمين:
- آخر نشاط في إدخال التقدم
- إحصائيات الجداول والتقدم

#### للمفتشين:
- آخر نشاط في الإشعارات
- إحصائيات الأساتذة المكلفين والإشعارات

## التغييرات المطبقة

### 1. تحديث app.py
```python
# إضافة معلومات آخر نشاط لجميع المستخدمين
stats['last_login'] = user.last_login
stats['created_at'] = user.created_at

# حساب عدد الأيام منذ آخر تسجيل دخول
if user.last_login:
    from datetime import datetime
    days_since_last_login = (datetime.utcnow() - user.last_login).days
    stats['days_since_last_login'] = days_since_last_login
else:
    stats['days_since_last_login'] = None
```

### 2. تحديث user_profile.html
- إضافة قسم معلومات آخر نشاط
- عرض تاريخ ووقت آخر تسجيل دخول
- مؤشرات بصرية لحالة النشاط
- تقييم شامل لحالة المستخدم

## المؤشرات البصرية

### ألوان المؤشرات:
- 🟢 **أخضر (نشط اليوم):** المستخدم سجل دخول اليوم
- 🔵 **أزرق (نشط خلال الأسبوع):** آخر نشاط خلال 7 أيام
- 🟡 **أصفر (غير نشط خلال الشهر):** آخر نشاط خلال 30 يوم
- 🔴 **أحمر (خامل):** آخر نشاط منذ أكثر من شهر وأقل من 6 أشهر
- ⚫ **أسود (مهجور):** آخر نشاط منذ أكثر من 6 أشهر
- 🔘 **رمادي (حساب جديد):** لم يسجل دخول بعد

### الرموز المستخدمة:
- ✅ `fa-check-circle`: نشط اليوم
- 🕐 `fa-clock`: نشط خلال الأسبوع
- ⚠️ `fa-exclamation-triangle`: غير نشط خلال الشهر
- ❌ `fa-times-circle`: خامل
- 🚫 `fa-ban`: مهجور
- 👤 `fa-user-clock`: حساب جديد

## الفوائد الإدارية

### 1. مراقبة النشاط
- تحديد المستخدمين النشطين والخاملين
- متابعة مستوى الاستخدام للنظام
- اكتشاف الحسابات المهجورة

### 2. اتخاذ القرارات
- تحديد المستخدمين الذين يحتاجون تذكير
- إدارة الحسابات غير النشطة
- تحسين استراتيجيات التفاعل

### 3. الأمان
- مراقبة الوصول غير المعتاد
- تحديد الحسابات المشبوهة
- إدارة أمان النظام

## كيفية الوصول

### للأدمن:
1. الذهاب إلى قائمة المستخدمين
2. النقر على أيقونة العين بجانب أي مستخدم
3. عرض الملف الشخصي مع معلومات النشاط

### لمدير المستخدمين:
1. الوصول إلى لوحة إدارة المستخدمين
2. اختيار مستخدم من القائمة
3. عرض تفاصيل النشاط والإحصائيات

## الرابط المباشر
```
http://127.0.0.1:5000/profile/view/<user_id>
```

## مثال على الاستخدام

### مستخدم نشط:
- آخر تسجيل دخول: 2025-08-02 في 14:30:25
- الحالة: نشط اليوم (مؤشر أخضر)
- عدد الأيام: 0 أيام

### مستخدم خامل:
- آخر تسجيل دخول: 2025-06-15 في 09:45:12
- الحالة: خامل - آخر نشاط منذ أكثر من شهر (مؤشر أحمر)
- عدد الأيام: 48 يوم
- تنبيه: قد يحتاج هذا المستخدم إلى متابعة أو تذكير بالنشاط

### حساب جديد:
- آخر تسجيل دخول: لم يسجل دخول بعد
- الحالة: حساب جديد (مؤشر رمادي)
- تاريخ الإنشاء: 2025-08-01 في 16:20:00

## التوافق

### المتطلبات:
- Flask-Login لتتبع آخر تسجيل دخول
- Bootstrap 5 للمؤشرات البصرية
- Font Awesome للرموز

### المتصفحات المدعومة:
- جميع المتصفحات الحديثة
- دعم كامل للعربية (RTL)

## الأمان والخصوصية

### الصلاحيات:
- متاح فقط للأدمن ومدير المستخدمين
- لا يمكن للمستخدمين العاديين رؤية هذه المعلومات
- حماية من الوصول غير المصرح

### البيانات المعروضة:
- تواريخ وأوقات آخر النشاط
- إحصائيات عامة فقط
- لا يتم عرض تفاصيل حساسة

---

**ملاحظة:** هذه الميزة تساعد الإدارة في مراقبة نشاط المستخدمين واتخاذ قرارات مدروسة حول إدارة الحسابات وتحسين تفاعل المستخدمين مع النظام.
