{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-7">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">
                    <i class="fas fa-user-plus animated-icon pulse-icon me-2"></i>
                    طلب إنشاء حساب جديد
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('register') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input class="form-control" id="username" name="username" type="text"
                                    placeholder="اسم المستخدم" required />
                                <label for="username">اسم المستخدم</label>
                                <div class="form-text text-muted mt-1">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن يكتب بالحروف اللاتينية
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input class="form-control" id="email" name="email" type="email"
                                    placeholder="البريد الإلكتروني" required />
                                <label for="email">البريد الإلكتروني</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0 position-relative">
                                <input class="form-control" id="password" name="password" type="password"
                                    placeholder="كلمة المرور" required />
                                <label for="password">كلمة المرور</label>
                                <button type="button" class="password-toggle-btn" id="togglePassword">
                                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                </button>
                                <div class="form-text text-muted small">
                                    <strong>متطلبات كلمة المرور:</strong><br>
                                    • أكثر من 6 أحرف<br>
                                    • حرف كبير واحد على الأقل (A-Z)<br>
                                    • رقم واحد على الأقل (0-9)<br>
                                    • رمز واحد على الأقل (!@#$%^&*(),.?":{}|<>)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0 position-relative">
                                <input class="form-control" id="confirm_password" name="confirm_password"
                                    type="password" placeholder="تأكيد كلمة المرور" required />
                                <label for="confirm_password">تأكيد كلمة المرور</label>
                                <button type="button" class="password-toggle-btn" id="toggleConfirmPassword">
                                    <i class="fas fa-eye" id="toggleConfirmPasswordIcon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input class="form-control" id="phone_number" name="phone_number" type="tel"
                                    placeholder="رقم الهاتف"
                                    pattern="^(0[5-7]\d{8}|0[2-4]\d{7}|\+213[5-7]\d{8}|\+213[2-4]\d{7})$"
                                    title="أدخل رقم هاتف جزائري صحيح (مثال: 0555123456)" required />
                                <label for="phone_number">رقم الهاتف *</label>
                            </div>
                            <div class="form-text text-muted">
                                <small>مثال: 0555123456 أو 021234567</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="wilaya_code" name="wilaya_code">
                                    <option value="">اختر الولاية (اختياري)</option>
                                    {% if wilayas %}
                                    {% for code, name in wilayas %}
                                    <option value="{{ code }}">{{ code }} - {{ name }}</option>
                                    {% endfor %}
                                    {% endif %}
                                </select>
                                <label for="wilaya_code">الولاية</label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-floating">
                            <select class="form-select" id="role" name="role" required>
                                <option value="" selected disabled>اختر الدور</option>
                                {% if enabled_roles %}
                                {% for role_value, role_display in enabled_roles %}
                                <option value="{{ role_value }}">{{ role_display }}</option>
                                {% endfor %}
                                {% else %}
                                <option value="teacher">أستاذ</option>
                                <option value="inspector">مفتش</option>
                                {% endif %}
                            </select>
                            <label for="role">الدور *</label>
                        </div>
                        <div class="form-text text-muted mt-2">
                            <i class="fas fa-info-circle me-1"></i>
                            ملاحظة: سيتم تفعيل حسابك من قبل الإدارة بعد المراجعة
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه مهم:</strong> بعد إرسال طلب الحساب، سيتم مراجعته من قبل الإدارة وتفعيله خلال 24-48
                        ساعة.
                    </div>

                    {% if show_success %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم إرسال طلبك بنجاح!</strong>
                        <br>
                        سيتم مراجعة طلبك وتفعيل حسابك خلال 24-48 ساعة.
                        <br>
                        <a href="{{ url_for('login') }}" class="btn btn-outline-success btn-sm mt-2">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            الانتقال لصفحة تسجيل الدخول
                        </a>
                    </div>
                    {% endif %}

                    {% if not show_success %}
                    <div class="d-grid">
                        <button class="btn btn-primary btn-lg btn-hover-effect" type="submit" id="submitBtn">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال طلب الحساب
                        </button>
                    </div>
                    {% else %}
                    <div class="d-grid">
                        <a href="{{ url_for('register') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إرسال طلب حساب آخر
                        </a>
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ url_for('login') }}">لديك حساب بالفعل؟ سجل دخولك!</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        const phoneField = document.getElementById('phone_number');
        const submitBtn = document.getElementById('submitBtn');

        // التحقق من قوة كلمة المرور
        function validatePasswordStrength() {
            const password = passwordField.value;

            if (!password) {
                passwordField.setCustomValidity('');
                passwordField.classList.remove('is-invalid', 'is-valid');
                return true;
            }

            // التحقق من المتطلبات
            const hasMinLength = password.length > 6;
            const hasUpperCase = /[A-Z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(password);

            if (!hasMinLength) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تكون أكثر من 6 أحرف');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasUpperCase) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasNumber) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasSymbol) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على رمز واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else {
                passwordField.setCustomValidity('');
                passwordField.classList.remove('is-invalid');
                passwordField.classList.add('is-valid');
                return true;
            }
        }

        // التحقق من تطابق كلمة المرور
        function validatePasswords() {
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;

            if (password && confirmPassword) {
                if (password !== confirmPassword) {
                    confirmPasswordField.setCustomValidity('كلمة المرور غير متطابقة');
                    confirmPasswordField.classList.add('is-invalid');
                    return false;
                } else {
                    confirmPasswordField.setCustomValidity('');
                    confirmPasswordField.classList.remove('is-invalid');
                    confirmPasswordField.classList.add('is-valid');
                    return true;
                }
            }
            return true;
        }

        // التحقق من رقم الهاتف الجزائري
        function validatePhone() {
            const phone = phoneField.value.replace(/[\s\-]/g, '');
            const patterns = [
                /^0[5-7]\d{8}$/,  // أرقام الجوال
                /^0[2-4]\d{7}$/,  // أرقام الهاتف الثابت
                /^\+213[5-7]\d{8}$/,  // أرقام دولية للجوال
                /^\+213[2-4]\d{7}$/   // أرقام دولية للثابت
            ];

            const isValid = patterns.some(pattern => pattern.test(phone));

            if (phone && !isValid) {
                phoneField.setCustomValidity('رقم الهاتف غير صحيح');
                phoneField.classList.add('is-invalid');
                return false;
            } else {
                phoneField.setCustomValidity('');
                phoneField.classList.remove('is-invalid');
                if (phone) phoneField.classList.add('is-valid');
                return true;
            }
        }

        // دوال إظهار/إخفاء كلمة المرور
        function setupPasswordToggle(passwordFieldId, toggleButtonId, toggleIconId) {
            const passwordField = document.getElementById(passwordFieldId);
            const toggleBtn = document.getElementById(toggleButtonId);
            const toggleIcon = document.getElementById(toggleIconId);

            if (passwordField && toggleBtn && toggleIcon) {
                toggleBtn.addEventListener('click', function () {
                    // تبديل نوع الحقل بين password و text
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // تبديل الأيقونة
                    if (type === 'text') {
                        toggleIcon.classList.remove('fa-eye');
                        toggleIcon.classList.add('fa-eye-slash');
                        toggleBtn.setAttribute('title', 'إخفاء كلمة المرور');
                    } else {
                        toggleIcon.classList.remove('fa-eye-slash');
                        toggleIcon.classList.add('fa-eye');
                        toggleBtn.setAttribute('title', 'إظهار كلمة المرور');
                    }
                });

                // إضافة تلميح للزر
                toggleBtn.setAttribute('title', 'إظهار كلمة المرور');
            }
        }

        // تفعيل إظهار/إخفاء كلمة المرور لكلا الحقلين
        setupPasswordToggle('password', 'togglePassword', 'togglePasswordIcon');
        setupPasswordToggle('confirm_password', 'toggleConfirmPassword', 'toggleConfirmPasswordIcon');

        // إضافة مستمعي الأحداث
        passwordField.addEventListener('input', function () {
            validatePasswordStrength();
            validatePasswords();
        });
        confirmPasswordField.addEventListener('input', validatePasswords);
        phoneField.addEventListener('input', validatePhone);

        // التحقق عند الإرسال
        document.querySelector('form').addEventListener('submit', function (e) {
            if (!validatePasswordStrength() || !validatePasswords() || !validatePhone()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    });
</script>

{% endblock %}