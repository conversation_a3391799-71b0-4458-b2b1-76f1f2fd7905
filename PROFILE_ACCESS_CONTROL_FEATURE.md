# ميزة التحكم في الوصول للملفات الشخصية

## الوصف
تم تطوير نظام تحكم متقدم في الوصول للملفات الشخصية للمستخدمين، بحيث يتم تطبيق قيود صارمة على من يمكنه رؤية ملف شخصي معين حسب الدور والصلاحيات.

## القواعد المطبقة

### 1. **الأدمن (Admin)**
- **يمكنه رؤية:** جميع الملفات الشخصية (أدمن، مديري مستخدمين، مفتشين، أساتذة)
- **لا توجد قيود:** الأدمن له صلاحية كاملة

### 2. **مديرو المستخدمين (User Manager)**
- **يمكنهم رؤية:** ملفات المفتشين والأساتذة فقط
- **لا يمكنهم رؤية:** ملفات الأدمن
- **السبب:** منع تسلسل الصلاحيات غير المرغوب فيه

### 3. **المفتشون والأساتذة**
- **لا يمكنهم الوصول:** لصفحة قائمة المستخدمين أو الملفات الشخصية للآخرين
- **يمكنهم رؤية:** ملفهم الشخصي فقط من خلال `/profile`

## التغييرات المطبقة

### 1. تحديث دالة `view_user_profile` في app.py

```python
@app.route('/profile/view/<int:user_id>')
@login_required
def view_user_profile(user_id):
    # التحقق من الصلاحيات الأساسية
    if current_user.role not in [Role.ADMIN, Role.USER_MANAGER]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على بيانات المستخدم
    user = User.query.get_or_404(user_id)
    
    # تطبيق قيود الوصول المحددة
    if current_user.role == Role.USER_MANAGER:
        # مديرو المستخدمين لا يمكنهم رؤية ملفات الأدمن
        if user.role == Role.ADMIN:
            flash('غير مصرح بالوصول لملفات الأدمن', 'danger')
            return redirect(url_for('users_list'))
    # الأدمن يمكنه رؤية جميع الملفات (لا توجد قيود إضافية)
```

### 2. تحديث قائمة المستخدمين (users_list.html)

```html
<!-- زر عرض الملف الشخصي مع قيود الوصول -->
{% if current_user.role == 'admin' or
      (current_user.role == 'user_manager' and user.role != 'admin') %}
<a href="{{ url_for('view_user_profile', user_id=user.id) }}"
    class="btn btn-outline-primary btn-sm" title="عرض الملف الشخصي">
    <i class="fas fa-eye"></i>
</a>
{% else %}
<button class="btn btn-outline-secondary btn-sm" disabled 
        title="غير مصرح بالوصول لملفات الأدمن">
    <i class="fas fa-eye-slash"></i>
</button>
{% endif %}
```

### 3. تحديث لوحة تحكم مدير المستخدمين (user_manager_dashboard.html)

```html
<!-- زر عرض الملف الشخصي مع قيود الوصول -->
{% if user.role != 'admin' %}
<a href="{{ url_for('view_user_profile', user_id=user.id) }}"
    class="btn btn-sm btn-outline-info me-1" title="عرض الملف الشخصي">
    <i class="fas fa-eye"></i>
</a>
{% else %}
<button class="btn btn-sm btn-outline-secondary me-1" disabled 
        title="غير مصرح بالوصول لملفات الأدمن">
    <i class="fas fa-eye-slash"></i>
</button>
{% endif %}
```

## الفوائد الأمنية

### 1. **منع تسلسل الصلاحيات**
- مديرو المستخدمين لا يمكنهم رؤية معلومات الأدمن الحساسة
- كل دور له حدود واضحة للوصول

### 2. **حماية المعلومات الحساسة**
- ملفات الأدمن محمية من الوصول غير المصرح به
- معلومات النشاط والإحصائيات محمية

### 3. **وضوح الصلاحيات**
- المستخدمون يرون بوضوح ما يمكنهم الوصول إليه
- الأزرار المعطلة تشير للقيود بوضوح

## التجربة البصرية

### للأدمن:
- **جميع الأزرار نشطة:** يمكن رؤية جميع الملفات
- **أيقونة العين:** 👁️ للملفات المتاحة

### لمديري المستخدمين:
- **أزرار نشطة:** للمفتشين والأساتذة
- **أزرار معطلة:** لملفات الأدمن
- **أيقونة العين المشطوبة:** 🚫👁️ للملفات المحظورة

### للمفتشين والأساتذة:
- **لا يمكن الوصول:** لقائمة المستخدمين أصلاً
- **الملف الشخصي فقط:** من خلال `/profile`

## رسائل الخطأ

### 1. **للوصول المباشر عبر URL:**
```
"غير مصرح بالوصول لملفات الأدمن"
```

### 2. **للأدوار غير المصرحة:**
```
"غير مصرح بالوصول"
```

### 3. **في tooltip الأزرار المعطلة:**
```
"غير مصرح بالوصول لملفات الأدمن"
```

## مصفوفة الصلاحيات

| الدور المُشاهِد | ملف الأدمن | ملف مدير المستخدمين | ملف المفتش | ملف الأستاذ |
|-----------------|------------|---------------------|------------|-------------|
| **الأدمن**      | ✅ نعم     | ✅ نعم              | ✅ نعم     | ✅ نعم      |
| **مدير المستخدمين** | ❌ لا   | ✅ نعم              | ✅ نعم     | ✅ نعم      |
| **المفتش**      | ❌ لا     | ❌ لا               | ❌ لا      | ❌ لا       |
| **الأستاذ**     | ❌ لا     | ❌ لا               | ❌ لا      | ❌ لا       |

## الحالات المختبرة

### 1. **الأدمن يحاول رؤية ملف أدمن آخر:**
- ✅ **النتيجة:** نجح الوصول
- **الصفحة:** تظهر الملف الشخصي كاملاً

### 2. **مدير المستخدمين يحاول رؤية ملف أدمن:**
- ❌ **النتيجة:** رفض الوصول
- **الرسالة:** "غير مصرح بالوصول لملفات الأدمن"
- **التوجيه:** العودة لقائمة المستخدمين

### 3. **مدير المستخدمين يحاول رؤية ملف مفتش:**
- ✅ **النتيجة:** نجح الوصول
- **الصفحة:** تظهر الملف الشخصي كاملاً

### 4. **محاولة الوصول المباشر عبر URL:**
- **للأدوار غير المصرحة:** رفض فوري
- **لمديري المستخدمين لملفات الأدمن:** رفض مع رسالة

## التوافق مع الميزات الأخرى

### 1. **ترتيب المستخدمين الإداريين:**
- يعمل بشكل طبيعي مع قيود الوصول
- الأدمن يظهر في الأعلى لكن مع قيود الوصول لمديري المستخدمين

### 2. **البحث والفلترة:**
- تعمل بشكل طبيعي
- قيود الوصول تطبق على النتائج

### 3. **إحصائيات النشاط:**
- محمية حسب قيود الوصول
- معلومات آخر نشاط محمية للأدمن

## الروابط ذات الصلة

- **قائمة المستخدمين:** `http://127.0.0.1:5000/users/list`
- **لوحة تحكم مدير المستخدمين:** `http://127.0.0.1:5000/dashboard/user_manager`
- **الملف الشخصي:** `http://127.0.0.1:5000/profile/view/<user_id>`

---

**ملاحظة:** هذا النظام يضمن الأمان والخصوصية في إدارة المستخدمين، ويمنع تسلسل الصلاحيات غير المرغوب فيه، مما يحافظ على سرية معلومات الأدمن ويضمن التحكم الصحيح في الوصول.
